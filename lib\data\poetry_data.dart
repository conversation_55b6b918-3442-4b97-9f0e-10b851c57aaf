import '../models/naming_model.dart';

/// 诗词数据类
class PoetryData {
  /// 唐诗数据
  static const List<Poetry> tangshi = [
    Poetry(
      content: "寥落古行宫，宫花寂寞红。白头宫女在，闲坐说玄宗。",
      title: "行宫",
      author: "元稹",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "空山不见人，但闻人语响。返景入深林，复照青苔上。",
      title: "鹿柴",
      author: "王维",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "君自故乡来，应知故乡事。来日绮窗前，寒梅著花未？",
      title: "杂诗三首·其二",
      author: "王维",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "红豆生南国，春来发几枝。愿君多采撷，此物最相思。",
      title: "相思",
      author: "王维",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "山中相送罢，日暮掩柴扉。春草明年绿，王孙归不归？",
      title: "送别",
      author: "王维",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "绿蚁新醅酒，红泥小火炉。晚来天欲雪，能饮一杯无？",
      title: "问刘十九",
      author: "白居易",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "白日依山尽，黄河入海流。欲穷千里目，更上一层楼。",
      title: "登鹳雀楼",
      author: "王之涣",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "独坐幽篁里，弹琴复长啸。深林人不知，明月来相照。",
      title: "竹里馆",
      author: "王维",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "向晚意不适，驱车登古原。夕阳无限好，只是近黄昏。",
      title: "乐游原",
      author: "李商隐",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
      title: "静夜思",
      author: "李白",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。",
      title: "春晓",
      author: "孟浩然",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "千山鸟飞绝，万径人踪灭。孤舟蓑笠翁，独钓寒江雪。",
      title: "江雪",
      author: "柳宗元",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "移舟泊烟渚，日暮客愁新。野旷天低树，江清月近人。",
      title: "宿建德江",
      author: "孟浩然",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "松下问童子，言师采药去。只在此山中，云深不知处。",
      title: "寻隐者不遇",
      author: "贾岛",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "寒雨连江夜入吴，平明送客楚山孤。洛阳亲友如相问，一片冰心在玉壶。",
      title: "芙蓉楼送辛渐",
      author: "王昌龄",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "独在异乡为异客，每逢佳节倍思亲。遥知兄弟登高处，遍插茱萸少一人。",
      title: "九月九日忆山东兄弟",
      author: "王维",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "故人西辞黄鹤楼，烟花三月下扬州。孤帆远影碧空尽，唯见长江天际流。",
      title: "黄鹤楼送孟浩然之广陵",
      author: "李白",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "朝辞白帝彩云间，千里江陵一日还。两岸猿声啼不住，轻舟已过万重山。",
      title: "早发白帝城",
      author: "李白",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "云母屏风烛影深，长河渐落晓星沉。嫦娥应悔偷灵药，碧海青天夜夜心。",
      title: "嫦娥",
      author: "李商隐",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "君问归期未有期，巴山夜雨涨秋池。何当共剪西窗烛，却话巴山夜雨时。",
      title: "夜雨寄北",
      author: "李商隐",
      book: "唐诗三百首",
      dynasty: "唐代",
    ),
  ];

  /// 宋词数据
  static const List<Poetry> songci = [
    Poetry(
      content: "明月几时有？把酒问青天。不知天上宫阙，今夕是何年。",
      title: "水调歌头·明月几时有",
      author: "苏轼",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "春花秋月何时了？往事知多少。小楼昨夜又东风，故国不堪回首月明中。",
      title: "虞美人·春花秋月何时了",
      author: "李煜",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "寻寻觅觅，冷冷清清，凄凄惨惨戚戚。乍暖还寒时候，最难将息。",
      title: "声声慢·寻寻觅觅",
      author: "李清照",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "红酥手，黄縢酒，满城春色宫墙柳。东风恶，欢情薄。",
      title: "钗头凤·红酥手",
      author: "陆游",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "无可奈何花落去，似曾相识燕归来。小园香径独徘徊。",
      title: "浣溪沙·一曲新词酒一杯",
      author: "晏殊",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "昨夜雨疏风骤，浓睡不消残酒。试问卷帘人，却道海棠依旧。",
      title: "如梦令·昨夜雨疏风骤",
      author: "李清照",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "大江东去，浪淘尽，千古风流人物。故垒西边，人道是，三国周郎赤壁。",
      title: "念奴娇·赤壁怀古",
      author: "苏轼",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "十年生死两茫茫，不思量，自难忘。千里孤坟，无处话凄凉。",
      title: "江城子·乙卯正月二十日夜记梦",
      author: "苏轼",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "莫听穿林打叶声，何妨吟啸且徐行。竹杖芒鞋轻胜马，谁怕？",
      title: "定风波·莫听穿林打叶声",
      author: "苏轼",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
    Poetry(
      content: "众里寻他千百度，蓦然回首，那人却在，灯火阑珊处。",
      title: "青玉案·元夕",
      author: "辛弃疾",
      book: "宋词三百首",
      dynasty: "宋代",
    ),
  ];

  /// 诗经数据
  static const List<Poetry> shijing = [
    Poetry(
      content: "关关雎鸠，在河之洲。窈窕淑女，君子好逑。",
      title: "关雎",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "蒹葭苍苍，白露为霜。所谓伊人，在水一方。",
      title: "蒹葭",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "青青子衿，悠悠我心。纵我不往，子宁不嗣音？",
      title: "子衿",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "桃之夭夭，灼灼其华。之子于归，宜其室家。",
      title: "桃夭",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "采采卷耳，不盈顷筐。嗟我怀人，寘彼周行。",
      title: "卷耳",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "呦呦鹿鸣，食野之苹。我有嘉宾，鼓瑟吹笙。",
      title: "鹿鸣",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "昔我往矣，杨柳依依。今我来思，雨雪霏霏。",
      title: "采薇",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
    Poetry(
      content: "投我以木瓜，报之以琼琚。匪报也，永以为好也。",
      title: "木瓜",
      author: null,
      book: "诗经",
      dynasty: "先秦",
    ),
  ];

  /// 楚辞数据
  static const List<Poetry> chuci = [
    Poetry(
      content: "路漫漫其修远兮，吾将上下而求索。",
      title: "离骚",
      author: "屈原",
      book: "楚辞",
      dynasty: "先秦",
    ),
    Poetry(
      content: "帝高阳之苗裔兮，朕皇考曰伯庸。",
      title: "离骚",
      author: "屈原",
      book: "楚辞",
      dynasty: "先秦",
    ),
    Poetry(
      content: "长太息以掩涕兮，哀民生之多艰。",
      title: "离骚",
      author: "屈原",
      book: "楚辞",
      dynasty: "先秦",
    ),
    Poetry(
      content: "亦余心之所善兮，虽九死其犹未悔。",
      title: "离骚",
      author: "屈原",
      book: "楚辞",
      dynasty: "先秦",
    ),
    Poetry(
      content: "袅袅兮秋风，洞庭波兮木叶下。",
      title: "湘夫人",
      author: "屈原",
      book: "楚辞",
      dynasty: "先秦",
    ),
    Poetry(
      content: "沅有芷兮澧有兰，思公子兮未敢言。",
      title: "湘夫人",
      author: "屈原",
      book: "楚辞",
      dynasty: "先秦",
    ),
  ];

  /// 乐府数据
  static const List<Poetry> yuefu = [
    Poetry(
      content: "青青园中葵，朝露待日晞。阳春布德泽，万物生光辉。",
      title: "长歌行",
      author: null,
      book: "乐府诗集",
      dynasty: "汉代",
    ),
    Poetry(
      content: "江南可采莲，莲叶何田田。鱼戏莲叶间，鱼戏莲叶东。",
      title: "江南",
      author: null,
      book: "乐府诗集",
      dynasty: "汉代",
    ),
    Poetry(
      content: "十五从军征，八十始得归。道逢乡里人，家中有阿谁？",
      title: "十五从军征",
      author: null,
      book: "乐府诗集",
      dynasty: "汉代",
    ),
    Poetry(
      content: "上邪！我欲与君相知，长命无绝衰。山无陵，江水为竭。",
      title: "上邪",
      author: null,
      book: "乐府诗集",
      dynasty: "汉代",
    ),
  ];

  /// 古诗数据
  static const List<Poetry> gushi = [
    Poetry(
      content: "慈母手中线，游子身上衣。临行密密缝，意恐迟迟归。",
      title: "游子吟",
      author: "孟郊",
      book: "古诗十九首",
      dynasty: "唐代",
    ),
    Poetry(
      content: "行行重行行，与君生别离。相去万余里，各在天一涯。",
      title: "行行重行行",
      author: null,
      book: "古诗十九首",
      dynasty: "汉代",
    ),
    Poetry(
      content: "青青河畔草，绵绵思远道。远道不可思，宿昔梦见之。",
      title: "青青河畔草",
      author: null,
      book: "古诗十九首",
      dynasty: "汉代",
    ),
    Poetry(
      content: "迢迢牵牛星，皎皎河汉女。纤纤擢素手，札札弄机杼。",
      title: "迢迢牵牛星",
      author: null,
      book: "古诗十九首",
      dynasty: "汉代",
    ),
    Poetry(
      content: "涉江采芙蓉，兰泽多芳草。采之欲遗谁，所思在远道。",
      title: "涉江采芙蓉",
      author: null,
      book: "古诗十九首",
      dynasty: "汉代",
    ),
  ];
}
