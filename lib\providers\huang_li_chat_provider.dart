import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/huang_li_chat_model.dart';
import '../models/huang_li_model.dart';
import '../models/gua_model.dart';
import '../services/global_ai_service.dart';
import '../services/universal_history_service.dart';
import '../utils/markdown_cleaner.dart';

class HuangLiChatProvider with ChangeNotifier {
  final GlobalAIService _aiService = GlobalAIService();
  
  HuangLiChatSession _currentSession = HuangLiChatSession(
    id: DateTime.now().millisecondsSinceEpoch.toString(),
  );
  
  bool _isLoading = false;
  bool _isStreaming = false;
  String _streamingContent = '';
  HuangLiData? _currentHuangLiData;
  
  // Getters
  HuangLiChatSession get currentSession => _currentSession;
  List<HuangLiChatMessage> get messages => _currentSession.messages;
  bool get isLoading => _isLoading;
  bool get isStreaming => _isStreaming;
  String get streamingContent => _streamingContent;
  HuangLiData? get currentHuangLiData => _currentHuangLiData;
  
  HuangLiChatProvider() {
    _initializeChat();
  }
  
  /// 初始化聊天
  void _initializeChat() {
    final welcomeMessage = HuangLiChatMessage.text(
      role: 'assistant',
      content: '您好！我是您的黄历解读专家 📅\n\n'
          '您可以问我关于黄历的任何问题，比如：\n'
          '• 今天下午2点适合洗澡吗？\n'
          '• 明天出行有什么需要注意的？\n'
          '• 这个时间搬家好不好？\n\n'
          '请告诉我您想了解的具体时间和要做的事情，我会根据黄历为您提供专业建议！',
    );

    _currentSession = _currentSession.addMessage(welcomeMessage);
    notifyListeners();
  }

  /// 更新当前黄历数据
  void updateHuangLiData(HuangLiData huangLiData) {
    _currentHuangLiData = huangLiData;
    _currentSession = _currentSession.updateFocusDate(huangLiData.solarDate);
    notifyListeners();
  }
  
  /// 发送用户消息并获取AI回复
  Future<void> sendMessage(String content, {
    String? specificTime,
    String? activity,
  }) async {
    if (content.trim().isEmpty || _isLoading || _currentHuangLiData == null) {
      return;
    }
    
    // 添加用户消息
    final userMessage = HuangLiChatMessage.userQuestion(
      content: content,
      relatedDate: _currentHuangLiData!.solarDate,
      relatedTime: specificTime,
      relatedActivity: activity,
    );
    
    _currentSession = _currentSession.addMessage(userMessage);
    notifyListeners();
    
    // 开始流式获取AI回复
    await _getAIResponseStream(
      question: content,
      specificTime: specificTime,
      activity: activity,
    );
  }
  
  /// 流式获取AI回复
  Future<void> _getAIResponseStream({
    required String question,
    String? specificTime,
    String? activity,
  }) async {
    _isLoading = true;
    _isStreaming = true;
    _streamingContent = '';
    notifyListeners();
    
    try {
      final stream = _aiService.getHuangLiAdviceStream(
        question: question,
        huangLiData: _currentHuangLiData!,
        specificTime: specificTime,
        activity: activity,
      );

      await for (final chunk in stream) {
        // 使用温和清理，保留emoji符号
        final cleanChunk = MarkdownCleaner.gentleClean(chunk);
        _streamingContent += cleanChunk;
        notifyListeners();
      }

      // 流式输出完成，添加完整的AI消息
      if (_streamingContent.isNotEmpty) {
        final aiMessage = HuangLiChatMessage.aiAdvice(
          content: _streamingContent,
          relatedDate: _currentHuangLiData!.solarDate,
        );

        _currentSession = _currentSession.addMessage(aiMessage);

        // 保存到历史记录
        await _saveToHistory(question, _streamingContent, specificTime, activity);
      }
      
    } catch (e) {
      // 错误处理
      final errorMessage = HuangLiChatMessage.text(
        role: 'assistant',
        content: '抱歉，获取黄历解读时出现了问题：${e.toString()}\n\n请稍后重试。',
      );
      
      _currentSession = _currentSession.addMessage(errorMessage);
    } finally {
      _isLoading = false;
      _isStreaming = false;
      _streamingContent = '';
      notifyListeners();
    }
  }
  
  /// 清空聊天记录
  void clearChat() {
    _currentSession = HuangLiChatSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      focusDate: _currentHuangLiData?.solarDate,
    );
    _initializeChat();
  }
  
  /// 重新开始聊天
  void restartChat() {
    clearChat();
  }
  
  /// 获取快捷问题建议
  List<String> getQuickQuestions() {
    if (_currentHuangLiData == null) {
      return [];
    }
    
    final date = _currentHuangLiData!.solarDate;
    final isToday = _isToday(date);
    final dateStr = isToday ? '今天' : '${date.month}月${date.day}日';
    
    return [
      '$dateStr适合出行吗？',
      '$dateStr搬家好不好？',
      '$dateStr开业怎么样？',
      '$dateStr结婚合适吗？',
      '$dateStr签合同可以吗？',
      '$dateStr装修房子好吗？',
    ];
  }
  
  /// 判断是否为今天
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }
  
  /// 解析用户输入中的时间和活动
  Map<String, String?> parseUserInput(String input) {
    String? time;
    String? activity;
    
    // 简单的时间解析
    final timePatterns = [
      RegExp(r'(上午|下午|早上|晚上|中午)\s*(\d{1,2})\s*[点时]'),
      RegExp(r'(\d{1,2})\s*[点时]\s*(上午|下午|早上|晚上)?'),
      RegExp(r'(早上|上午|中午|下午|晚上)'),
    ];
    
    for (final pattern in timePatterns) {
      final match = pattern.firstMatch(input);
      if (match != null) {
        time = match.group(0);
        break;
      }
    }
    
    // 简单的活动解析
    final activityPatterns = [
      '洗澡', '出行', '搬家', '开业', '结婚', '签合同', '装修', '买车', '买房',
      '面试', '考试', '手术', '剪头发', '理发', '约会', '聚餐', '旅游', '投资',
    ];
    
    for (final act in activityPatterns) {
      if (input.contains(act)) {
        activity = act;
        break;
      }
    }
    
    return {
      'time': time,
      'activity': activity,
    };
  }

  /// 保存对话到历史记录
  Future<void> _saveToHistory(
    String question,
    String answer,
    String? specificTime,
    String? activity,
  ) async {
    if (_currentHuangLiData == null) return;

    try {
      // 构建黄历信息摘要
      final huangLiInfo = <String, dynamic>{
        '日期': '${_currentHuangLiData!.solarDate.year}年${_currentHuangLiData!.solarDate.month}月${_currentHuangLiData!.solarDate.day}日',
        '农历': '${_currentHuangLiData!.lunarYearInChinese}年${_currentHuangLiData!.lunarMonthInChinese}月${_currentHuangLiData!.lunarDayInChinese}',
        '干支': '${_currentHuangLiData!.dayGanZhi}日',
        '宜': _currentHuangLiData!.dayYi.take(3).join('、'),
        '忌': _currentHuangLiData!.dayJi.take(3).join('、'),
      };

      final huangLiAIResult = HuangLiAIResult(
        question: question,
        answer: answer,
        consultDate: _currentHuangLiData!.solarDate,
        relatedTime: specificTime,
        relatedActivity: activity,
        huangLiInfo: huangLiInfo,
      );

      await UniversalHistoryService.addHuangLiAIRecord(huangLiAIResult);
      debugPrint('黄历AI咨询记录已保存');
    } catch (e) {
      debugPrint('保存黄历AI咨询记录失败: $e');
      // 不影响主流程，只记录错误
    }
  }


}
