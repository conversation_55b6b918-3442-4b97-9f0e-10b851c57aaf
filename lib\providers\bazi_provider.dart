import 'package:flutter/foundation.dart';
import '../models/bazi_model.dart';
import '../models/api_config.dart';
import '../services/bazi_service.dart';
import '../services/global_ai_service.dart';
import '../services/universal_history_service.dart';

class BaziProvider extends ChangeNotifier {
  BaziModel? _currentBazi;
  bool _isLoading = false;
  String? _error;
  bool _isAnalyzing = false;
  String _aiAnalysis = '';

  BaziModel? get currentBazi => _currentBazi;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAnalyzing => _isAnalyzing;
  String get aiAnalysis => _aiAnalysis;

  /// 计算八字排盘
  Future<void> calculateBazi({
    required String name,
    required int gender,
    required DateTime birthTime,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // 计算八字排盘
      final chart = BaziService.calculateBazi(
        name: name,
        gender: gender,
        birthTime: birthTime,
      );

      // 生成基础分析
      final analysis = BaziService.generateAnalysis(chart, gender);

      // 创建八字模型
      _currentBazi = BaziModel(
        name: name,
        gender: gender,
        birthTime: birthTime,
        chart: chart,
        analysis: analysis,
        createdAt: DateTime.now(),
      );

      // 保存到历史记录
      await UniversalHistoryService.addBaziRecord(_currentBazi!);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = '计算八字失败: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 使用AI分析八字
  Future<void> analyzeWithAI() async {
    if (_currentBazi == null) return;

    _isAnalyzing = true;
    _aiAnalysis = '';
    notifyListeners();

    try {
      final prompt = _buildAIPrompt(_currentBazi!);
      final systemPrompt = '''
你是一位专业的八字命理师，精通传统命理学。请根据提供的八字信息进行详细、准确的分析。
分析要专业但通俗易懂，给出具体的建议和指导。
请提供完整详细的分析，不要省略任何重要内容。
请从以下几个方面进行全面分析：
1. 基本命格分析
2. 五行强弱分析
3. 十神关系分析
4. 大运流年分析
5. 性格特点分析
6. 事业财运分析
7. 感情婚姻分析
8. 健康状况分析
9. 人生建议和注意事项
''';

      // 创建专门用于八字分析的配置，使用更大的token限制
      final baziConfig = ApiConfig.defaultConfig.copyWith(
        maxTokens: 4000, // 增加到4000 tokens，确保完整输出
        temperature: 0.8, // 稍微提高创造性
      );

      // 使用流式输出获取AI分析
      final stream = GlobalAIService().chatStream(
        systemPrompt: systemPrompt,
        userMessage: prompt,
        config: baziConfig,
      );

      await for (final chunk in stream) {
        if (chunk.isNotEmpty) {
          _aiAnalysis += chunk;
          notifyListeners();
        }
      }

      // 确保分析完成后状态正确
      _isAnalyzing = false;
      notifyListeners();

      // 如果有分析结果，保存到历史记录
      if (_aiAnalysis.isNotEmpty && _currentBazi != null) {
        await _saveToHistory();
      }

    } catch (e) {
      _error = 'AI分析失败: $e';
      _isAnalyzing = false;
      _aiAnalysis = _aiAnalysis.isEmpty ? '分析失败，请重试' : _aiAnalysis;
      notifyListeners();
    }
  }

  /// 保存到历史记录
  Future<void> _saveToHistory() async {
    if (_currentBazi == null || _aiAnalysis.isEmpty) return;

    try {
      // 创建包含AI分析的八字模型
      final baziWithAnalysis = BaziModel(
        name: _currentBazi!.name,
        gender: _currentBazi!.gender,
        birthTime: _currentBazi!.birthTime,
        chart: _currentBazi!.chart,
        analysis: BaziAnalysis(
          wuxingAnalysis: _aiAnalysis, // 将完整的AI分析保存到这里
          personalityAnalysis: _aiAnalysis, // 同时保存到其他字段以确保完整性
          careerAnalysis: _aiAnalysis,
          loveAnalysis: _aiAnalysis,
          healthAnalysis: _aiAnalysis,
          luckAnalysis: _aiAnalysis,
        ),
        createdAt: DateTime.now(),
      );

      await UniversalHistoryService.addBaziRecord(baziWithAnalysis);
    } catch (e) {
      // 保存失败不影响主要功能
      print('保存八字记录失败: $e');
    }
  }

  /// 构建AI分析提示词
  String _buildAIPrompt(BaziModel bazi) {
    final chart = bazi.chart;
    final genderText = bazi.gender == 1 ? '男' : '女';
    
    return '''
你是一位专业的八字命理师，请根据以下八字信息进行详细分析：

基本信息：
- 姓名：${bazi.name}
- 性别：$genderText
- 出生时间：${chart.solarDate}
- 农历：${chart.lunarDate}
- 生肖：${chart.zodiac}
- 星座：${chart.constellation}

八字排盘：
年柱：${chart.year.ganzhi} (${chart.year.nayin})
月柱：${chart.month.ganzhi} (${chart.month.nayin})
日柱：${chart.day.ganzhi} (${chart.day.nayin})
时柱：${chart.time.ganzhi} (${chart.time.nayin})

五行分布：
年：${chart.year.wuxing}
月：${chart.month.wuxing}
日：${chart.day.wuxing}
时：${chart.time.wuxing}

十神分析：
年：${chart.year.zhuxing}
月：${chart.month.zhuxing}
日：日主
时：${chart.time.zhuxing}

大运信息：
${chart.luck.startLabel}
起运公历：${chart.luck.startSolar}

请从以下几个方面进行详细分析：

1. 五行分析：分析五行强弱、喜忌用神
2. 性格特征：根据日主和格局分析性格特点
3. 事业运势：分析适合的职业方向和事业发展
4. 财运分析：分析财运状况和理财建议
5. 感情婚姻：分析感情运势和婚姻状况
6. 健康状况：根据五行分析健康注意事项
7. 大运流年：分析当前和未来的运势变化

请用专业但通俗易懂的语言进行分析，给出具体的建议和指导。
''';
  }

  /// 清除当前八字
  void clearBazi() {
    _currentBazi = null;
    _error = null;
    _aiAnalysis = '';
    notifyListeners();
  }

  /// 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 获取八字表格数据
  List<Map<String, String>> getBaziTableData() {
    if (_currentBazi == null) return [];

    final chart = _currentBazi!.chart;
    final genderText = _currentBazi!.gender == 1 ? '元男' : '元女';

    return [
      {
        'name': '主星',
        'year': chart.year.zhuxing,
        'month': chart.month.zhuxing,
        'day': genderText,
        'time': chart.time.zhuxing,
      },
      {
        'name': '天干',
        'year': chart.year.tiangan,
        'month': chart.month.tiangan,
        'day': chart.day.tiangan,
        'time': chart.time.tiangan,
      },
      {
        'name': '地支',
        'year': chart.year.dizhi,
        'month': chart.month.dizhi,
        'day': chart.day.dizhi,
        'time': chart.time.dizhi,
      },
      {
        'name': '五行',
        'year': chart.year.wuxing,
        'month': chart.month.wuxing,
        'day': chart.day.wuxing,
        'time': chart.time.wuxing,
      },
      {
        'name': '藏干',
        'year': chart.year.canggan.join('\n'),
        'month': chart.month.canggan.join('\n'),
        'day': chart.day.canggan.join('\n'),
        'time': chart.time.canggan.join('\n'),
      },
      {
        'name': '副星',
        'year': chart.year.fuxing.join('\n'),
        'month': chart.month.fuxing.join('\n'),
        'day': chart.day.fuxing.join('\n'),
        'time': chart.time.fuxing.join('\n'),
      },
      {
        'name': '星运',
        'year': chart.year.dishi,
        'month': chart.month.dishi,
        'day': chart.day.dishi,
        'time': chart.time.dishi,
      },
      {
        'name': '纳音',
        'year': chart.year.nayin,
        'month': chart.month.nayin,
        'day': chart.day.nayin,
        'time': chart.time.nayin,
      },
    ];
  }
}
