{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///F:/video/2021name/newName/App.vue?727c", "uni-app:///App.vue", "webpack:///F:/video/2021name/newName/App.vue?9ea0", "webpack:///F:/video/2021name/newName/App.vue?02e6"], "names": ["<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "app", "$mount"], "mappings": ";;;;;;;;;iDAAA,wCAAmB;AACnB,uE;;AAEAA,aAAIC,MAAJ,CAAWC,aAAX,GAA2B,KAA3B;;AAEAC,aAAIC,MAAJ,GAAa,KAAb;;AAEA,IAAMC,GAAG,GAAG,IAAIL,YAAJ;AACLG,YADK,EAAZ;;AAGA,UAAAE,GAAG,EAACC,MAAJ,G;;;;;;;;;;;;;;;;ACVA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,6MAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAwvB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;ACC5wB;AACA;AACA;AACA,GAHA;AAIA;AACA;AACA,GANA;AAOA;AACA;AACA,GATA,E;;;;;;;;;;;ACDA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,kkCAAG,EAAC,C;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';import Vue from 'vue'\r\nimport App from './App'\r\n\r\nVue.config.productionTip = false\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n    ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--12-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--12-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\n", "import mod from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1619072842557\n      var cssReload = require(\"E:/HBuilderX.2.2.2.20190816.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}