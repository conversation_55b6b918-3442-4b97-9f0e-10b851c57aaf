import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/naming_provider.dart';
import '../models/naming_model.dart';
import '../widgets/naming_result_card.dart';
import '../widgets/name_analysis_widget.dart';

class NamingScreen extends StatefulWidget {
  const NamingScreen({super.key});

  @override
  State<NamingScreen> createState() => _NamingScreenState();
}

class _NamingScreenState extends State<NamingScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _surnameController = TextEditingController();
  final TextEditingController _nameAnalysisController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _surnameController.dispose();
    _nameAnalysisController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('起名测名'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1A1A1A),
        elevation: 1,
        shadowColor: const Color(0xFFE5E5E5),
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF1A1A1A),
          unselectedLabelColor: const Color(0xFF666666),
          indicatorColor: const Color(0xFF1A1A1A),
          labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          unselectedLabelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
          tabs: const [
            Tab(text: '诗词起名'),
            Tab(text: '姓名测试'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNamingTab(),
          _buildAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildNamingTab() {
    return Consumer<NamingProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 姓氏输入
              _buildSurnameInput(provider),
              const SizedBox(height: 20),
              
              // 诗词类型选择
              _buildPoetryTypeSelection(provider),
              const SizedBox(height: 24),
              
              // 起名按钮
              _buildGenerateButton(provider),
              const SizedBox(height: 24),
              
              // 错误信息
              if (provider.namingError != null)
                _buildErrorMessage(provider.namingError!),
              
              // 起名结果
              if (provider.namingResults.isNotEmpty)
                _buildNamingResults(provider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnalysisTab() {
    return Consumer<NamingProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 姓名输入
              _buildNameInput(provider),
              const SizedBox(height: 24),
              
              // 分析按钮
              _buildAnalyzeButton(provider),
              const SizedBox(height: 24),
              
              // 错误信息
              if (provider.analysisError != null)
                _buildErrorMessage(provider.analysisError!),
              
              // 分析结果
              if (provider.currentAnalysisName.isNotEmpty)
                NameAnalysisWidget(
                  name: provider.currentAnalysisName,
                  analysis: provider.nameAnalysis,
                  isLoading: provider.isAnalyzingName,
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSurnameInput(NamingProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '请输入姓氏',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1A1A1A),
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _surnameController,
          onChanged: provider.setSurname,
          decoration: InputDecoration(
            hintText: '如：王、李、张',
            hintStyle: TextStyle(
              color: const Color(0xFF666666),
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE5E5E5)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE5E5E5)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF1A1A1A), width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF1A1A1A),
          ),
        ),
      ],
    );
  }

  Widget _buildPoetryTypeSelection(NamingProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择诗词类型',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1A1A1A),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 8,
          children: PoetryType.values.map((type) {
            final isSelected = provider.selectedPoetryType == type;
            return GestureDetector(
              onTap: () => provider.setPoetryType(type),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFF1A1A1A) : Colors.white,
                  border: Border.all(
                    color: isSelected ? const Color(0xFF1A1A1A) : const Color(0xFFE5E5E5),
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  type.displayName,
                  style: TextStyle(
                    color: isSelected ? Colors.white : const Color(0xFF666666),
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildGenerateButton(NamingProvider provider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: provider.isGeneratingNames ? null : provider.generateNames,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1A1A1A),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: provider.isGeneratingNames
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                '开始起名',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildNameInput(NamingProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '请输入要分析的姓名',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1A1A1A),
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _nameAnalysisController,
          decoration: InputDecoration(
            hintText: '如：王小明、李雅静',
            hintStyle: TextStyle(
              color: const Color(0xFF666666),
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE5E5E5)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE5E5E5)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF1A1A1A), width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF1A1A1A),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalyzeButton(NamingProvider provider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: provider.isAnalyzingName 
            ? null 
            : () => provider.analyzeName(_nameAnalysisController.text.trim()),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1A1A1A),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: provider.isAnalyzingName
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                '开始分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        error,
        style: TextStyle(
          color: Colors.red.shade700,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildNamingResults(NamingProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '起名结果',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1A1A1A),
          ),
        ),
        const SizedBox(height: 12),
        ...provider.namingResults.map((result) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: NamingResultCard(
            result: result,
            onSave: () => provider.saveNamingResultToHistory(result),
          ),
        )).toList(),
      ],
    );
  }
}
