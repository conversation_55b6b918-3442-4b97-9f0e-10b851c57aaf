import 'package:flutter_test/flutter_test.dart';
import 'package:daotianji/utils/markdown_cleaner.dart';

void main() {
  group('Markdown符号清理测试', () {
    test('MarkdownCleaner应该清理Markdown符号', () {
      // 测试各种Markdown格式
      const testText = '''
# 这是标题
## 二级标题
**这是粗体文本**
*这是斜体文本*
- 这是列表项
1. 这是数字列表
`这是代码`
```
这是代码块
```
> 这是引用
[链接文本](http://example.com)
_下划线文本_
~删除线~
''';

      final cleanedText = MarkdownCleaner.clean(testText);
      
      // 验证Markdown符号被移除
      expect(cleanedText.contains('#'), false);
      expect(cleanedText.contains('**'), false);
      expect(cleanedText.contains('*'), false);
      expect(cleanedText.contains('`'), false);
      expect(cleanedText.contains('['), false);
      expect(cleanedText.contains(']'), false);
      expect(cleanedText.contains('('), false);
      expect(cleanedText.contains(')'), false);
      expect(cleanedText.contains('_'), false);
      expect(cleanedText.contains('~'), false);
      
      // 验证文本内容保留
      expect(cleanedText.contains('这是标题'), true);
      expect(cleanedText.contains('二级标题'), true);
      expect(cleanedText.contains('这是粗体文本'), true);
      expect(cleanedText.contains('这是斜体文本'), true);
      expect(cleanedText.contains('这是列表项'), true);
      expect(cleanedText.contains('这是数字列表'), true);
      expect(cleanedText.contains('这是代码'), true);
      expect(cleanedText.contains('这是代码块'), true);
      expect(cleanedText.contains('这是引用'), true);
      expect(cleanedText.contains('链接文本'), true);
      expect(cleanedText.contains('下划线文本'), true);
      expect(cleanedText.contains('删除线'), true);
    });

    test('应该清理姓名分析中的Markdown符号', () {
      const testText = '**姓名分析**：这个名字*很好*，具有以下特点：\n- 寓意深刻\n- 音韵优美';
      final cleanedText = MarkdownCleaner.clean(testText);

      expect(cleanedText.contains('**'), false);
      expect(cleanedText.contains('*'), false);
      expect(cleanedText.contains('-'), false);
      expect(cleanedText.contains('姓名分析'), true);
      expect(cleanedText.contains('很好'), true);
      expect(cleanedText.contains('寓意深刻'), true);
      expect(cleanedText.contains('音韵优美'), true);
    });

    test('应该清理六爻解读中的Markdown符号', () {
      const testText = '''
## 卦象解读
**乾卦**代表天，象征：
1. 刚健进取
2. 自强不息
> 建议：保持积极态度
''';

      final cleanedText = MarkdownCleaner.clean(testText);

      expect(cleanedText.contains('#'), false);
      expect(cleanedText.contains('**'), false);
      expect(cleanedText.contains('1.'), false);
      expect(cleanedText.contains('2.'), false);
      expect(cleanedText.contains('>'), false);
      expect(cleanedText.contains('卦象解读'), true);
      expect(cleanedText.contains('乾卦'), true);
      expect(cleanedText.contains('刚健进取'), true);
      expect(cleanedText.contains('自强不息'), true);
      expect(cleanedText.contains('建议：保持积极态度'), true);
    });

    test('应该处理复杂的嵌套Markdown格式', () {
      const testText = '''
# **重要提醒**
这是一个包含*多种*格式的文本：
- **粗体列表项**
- *斜体列表项*
- `代码列表项`

> **引用中的粗体**和*斜体*

[**粗体链接**](http://example.com)
''';

      final cleanedText = MarkdownCleaner.clean(testText);
      
      // 验证所有Markdown符号都被清理
      expect(cleanedText.contains('#'), false);
      expect(cleanedText.contains('**'), false);
      expect(cleanedText.contains('*'), false);
      expect(cleanedText.contains('`'), false);
      expect(cleanedText.contains('>'), false);
      expect(cleanedText.contains('['), false);
      expect(cleanedText.contains(']'), false);
      expect(cleanedText.contains('('), false);
      expect(cleanedText.contains(')'), false);
      
      // 验证文本内容保留
      expect(cleanedText.contains('重要提醒'), true);
      expect(cleanedText.contains('多种'), true);
      expect(cleanedText.contains('粗体列表项'), true);
      expect(cleanedText.contains('斜体列表项'), true);
      expect(cleanedText.contains('代码列表项'), true);
      expect(cleanedText.contains('引用中的粗体'), true);
      expect(cleanedText.contains('粗体链接'), true);
    });

    test('应该处理空字符串和特殊情况', () {
      // 空字符串
      expect(MarkdownCleaner.clean(''), '');

      // 只有Markdown符号
      expect(MarkdownCleaner.clean('***'), '');
      expect(MarkdownCleaner.clean('###'), '');
      expect(MarkdownCleaner.clean('```'), '');

      // 纯文本（无Markdown）
      const pureText = '这是纯文本，没有任何格式符号。';
      expect(MarkdownCleaner.clean(pureText), pureText);
    });
  });
}
