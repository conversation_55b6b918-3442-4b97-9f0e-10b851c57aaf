import 'package:json_annotation/json_annotation.dart';

part 'bazi_model.g.dart';

@JsonSerializable()
class BaziModel {
  final String name;
  final int gender; // 0: 女, 1: 男
  final DateTime birthTime;
  final BaziChart chart;
  final BaziAnalysis analysis;
  final DateTime createdAt;

  const BaziModel({
    required this.name,
    required this.gender,
    required this.birthTime,
    required this.chart,
    required this.analysis,
    required this.createdAt,
  });

  factory BaziModel.fromJson(Map<String, dynamic> json) =>
      _$BaziModelFromJson(json);

  Map<String, dynamic> toJson() => _$BaziModelToJson(this);
}

@JsonSerializable()
class BaziChart {
  final BaziPillar year;
  final BaziPillar month;
  final BaziPillar day;
  final BaziPillar time;
  final BaziLuck luck;
  final String zodiac;
  final String constellation;
  final String lunarDate;
  final String solarDate;

  const BaziChart({
    required this.year,
    required this.month,
    required this.day,
    required this.time,
    required this.luck,
    required this.zodiac,
    required this.constellation,
    required this.lunarDate,
    required this.solarDate,
  });

  factory BaziChart.fromJson(Map<String, dynamic> json) =>
      _$BaziChartFromJson(json);

  Map<String, dynamic> toJson() => _$BaziChartToJson(this);
}

@JsonSerializable()
class BaziPillar {
  final String tiangan; // 天干
  final String dizhi; // 地支
  final String wuxing; // 五行
  final String nayin; // 纳音
  final String dishi; // 地势
  final List<String> canggan; // 藏干
  final String zhuxing; // 主星
  final List<String> fuxing; // 副星

  const BaziPillar({
    required this.tiangan,
    required this.dizhi,
    required this.wuxing,
    required this.nayin,
    required this.dishi,
    required this.canggan,
    required this.zhuxing,
    required this.fuxing,
  });

  factory BaziPillar.fromJson(Map<String, dynamic> json) =>
      _$BaziPillarFromJson(json);

  Map<String, dynamic> toJson() => _$BaziPillarToJson(this);

  String get ganzhi => '$tiangan$dizhi';
}

@JsonSerializable()
class BaziLuck {
  final int startYear;
  final int startMonth;
  final int startDay;
  final int startHour;
  final String startSolar;
  final List<BaziDayun> dayun;

  const BaziLuck({
    required this.startYear,
    required this.startMonth,
    required this.startDay,
    required this.startHour,
    required this.startSolar,
    required this.dayun,
  });

  factory BaziLuck.fromJson(Map<String, dynamic> json) =>
      _$BaziLuckFromJson(json);

  Map<String, dynamic> toJson() => _$BaziLuckToJson(this);

  String get startLabel {
    String str = '出生后';
    if (startYear > 0) str += '${startYear}年';
    if (startMonth > 0) str += '${startMonth}月';
    if (startDay > 0) str += '${startDay}日';
    if (startHour > 0) str += '${startHour}时';
    str += '后起运';
    return str;
  }
}

@JsonSerializable()
class BaziDayun {
  final String ganzhi;
  final int startAge;
  final int endAge;
  final String startYear;
  final String endYear;

  const BaziDayun({
    required this.ganzhi,
    required this.startAge,
    required this.endAge,
    required this.startYear,
    required this.endYear,
  });

  factory BaziDayun.fromJson(Map<String, dynamic> json) =>
      _$BaziDayunFromJson(json);

  Map<String, dynamic> toJson() => _$BaziDayunToJson(this);
}

@JsonSerializable()
class BaziAnalysis {
  final String wuxingAnalysis; // 五行分析
  final String personalityAnalysis; // 性格分析
  final String careerAnalysis; // 事业分析
  final String loveAnalysis; // 感情分析
  final String healthAnalysis; // 健康分析
  final String luckAnalysis; // 运势分析

  const BaziAnalysis({
    required this.wuxingAnalysis,
    required this.personalityAnalysis,
    required this.careerAnalysis,
    required this.loveAnalysis,
    required this.healthAnalysis,
    required this.luckAnalysis,
  });

  factory BaziAnalysis.fromJson(Map<String, dynamic> json) =>
      _$BaziAnalysisFromJson(json);

  Map<String, dynamic> toJson() => _$BaziAnalysisToJson(this);
}

// 八字配置常量
class BaziConstants {
  static const List<String> tiangan = [
    '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'
  ];

  static const List<String> dizhi = [
    '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'
  ];

  static const List<String> wuxing = [
    '木', '木', '火', '火', '土', '土', '金', '金', '水', '水'
  ];

  static const List<String> dizhiWuxing = [
    '水', '土', '木', '木', '土', '火', '火', '土', '金', '金', '土', '水'
  ];

  static const List<String> zodiac = [
    '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'
  ];

  static const Map<String, Map<String, String>> shishenMap = {
    '甲': {
      '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财',
      '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印'
    },
    '乙': {
      '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财',
      '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印'
    },
    '丙': {
      '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神',
      '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官'
    },
    '丁': {
      '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官',
      '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀'
    },
    '戊': {
      '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩',
      '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财'
    },
    '己': {
      '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财',
      '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财'
    },
    '庚': {
      '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印',
      '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官'
    },
    '辛': {
      '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印',
      '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神'
    },
    '壬': {
      '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀',
      '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财'
    },
    '癸': {
      '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官',
      '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩'
    },
  };

  static const Map<String, String> shishenShort = {
    '正印': '印', '正官': '官', '劫财': '劫', '伤官': '伤', '正财': '财',
    '七杀': '杀', '偏印': '枭', '比肩': '比', '食神': '食', '偏财': '才',
  };
}
