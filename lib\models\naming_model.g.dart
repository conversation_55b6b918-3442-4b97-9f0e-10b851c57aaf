// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'naming_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Poetry _$PoetryFromJson(Map<String, dynamic> json) => Poetry(
      content: json['content'] as String,
      title: json['title'] as String,
      author: json['author'] as String?,
      book: json['book'] as String,
      dynasty: json['dynasty'] as String,
    );

Map<String, dynamic> _$PoetryToJson(Poetry instance) => <String, dynamic>{
      'content': instance.content,
      'title': instance.title,
      'author': instance.author,
      'book': instance.book,
      'dynasty': instance.dynasty,
    };

NamingResult _$NamingResultFromJson(Map<String, dynamic> json) => NamingResult(
      surname: json['surname'] as String,
      name: (json['name'] as List<dynamic>).map((e) => e as String).toList(),
      sentence: (json['sentence'] as List<dynamic>).map((e) => e as String).toList(),
      title: json['title'] as String,
      author: json['author'] as String,
      book: json['book'] as String,
      dynasty: json['dynasty'] as String,
      poetryType: $enumDecode(_$PoetryTypeEnumMap, json['poetryType']),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$NamingResultToJson(NamingResult instance) =>
    <String, dynamic>{
      'surname': instance.surname,
      'name': instance.name,
      'sentence': instance.sentence,
      'title': instance.title,
      'author': instance.author,
      'book': instance.book,
      'dynasty': instance.dynasty,
      'poetryType': _$PoetryTypeEnumMap[instance.poetryType]!,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$PoetryTypeEnumMap = {
  PoetryType.tangshi: '唐诗',
  PoetryType.songci: '宋词',
  PoetryType.shijing: '诗经',
  PoetryType.chuci: '楚辞',
  PoetryType.yuefu: '乐府',
  PoetryType.gushi: '古诗',
};

NameAnalysisResult _$NameAnalysisResultFromJson(Map<String, dynamic> json) =>
    NameAnalysisResult(
      fullName: json['fullName'] as String,
      aiAnalysis: json['aiAnalysis'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$NameAnalysisResultToJson(NameAnalysisResult instance) =>
    <String, dynamic>{
      'fullName': instance.fullName,
      'aiAnalysis': instance.aiAnalysis,
      'createdAt': instance.createdAt.toIso8601String(),
    };

NamingRequest _$NamingRequestFromJson(Map<String, dynamic> json) =>
    NamingRequest(
      surname: json['surname'] as String,
      poetryType: $enumDecode(_$PoetryTypeEnumMap, json['poetryType']),
    );

Map<String, dynamic> _$NamingRequestToJson(NamingRequest instance) =>
    <String, dynamic>{
      'surname': instance.surname,
      'poetryType': _$PoetryTypeEnumMap[instance.poetryType]!,
    };

NameAnalysisRequest _$NameAnalysisRequestFromJson(Map<String, dynamic> json) =>
    NameAnalysisRequest(
      fullName: json['fullName'] as String,
    );

Map<String, dynamic> _$NameAnalysisRequestToJson(
        NameAnalysisRequest instance) =>
    <String, dynamic>{
      'fullName': instance.fullName,
    };

K $enumDecode<K, V>(
  Map<K, V> enumValues,
  Object? source, {
  K? unknownValue,
}) {
  if (source == null) {
    throw ArgumentError(
      'A value must be provided. Supported values: '
      '${enumValues.values.join(', ')}',
    );
  }

  return enumValues.entries.singleWhere(
    (e) => e.value == source,
    orElse: () {
      if (unknownValue == null) {
        throw ArgumentError(
          '`$source` is not one of the supported values: '
          '${enumValues.values.join(', ')}',
        );
      }
      return MapEntry(unknownValue, enumValues.values.first);
    },
  ).key;
}
