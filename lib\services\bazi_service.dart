import 'package:lunar/lunar.dart';
import 'package:intl/intl.dart';
import '../models/bazi_model.dart';

class BaziService {
  /// 计算八字排盘
  static BaziChart calculateBazi({
    required String name,
    required int gender,
    required DateTime birthTime,
  }) {
    final solar = Solar.fromDate(birthTime);
    final lunar = solar.getLunar();
    final bazi = lunar.getEightChar();
    final yun = bazi.getYun(gender);

    // 计算四柱
    final yearPillar = _calculatePillar(
      bazi.getYear(),
      bazi.getYearGan(),
      bazi.getYearZhi(),
      bazi.getYearWuXing(),
      bazi.getYearNaYin(),
      bazi.getYearDiShi(),
      bazi.getYearHideGan(),
      bazi.getYearShiShenGan(),
      bazi.getYearShiShenZhi(),
    );

    final monthPillar = _calculatePillar(
      bazi.getMonth(),
      bazi.getMonthGan(),
      bazi.getMonthZhi(),
      bazi.getMonthWuXing(),
      bazi.getMonthNaYin(),
      bazi.getMonthDiShi(),
      bazi.getMonthHideGan(),
      bazi.getMonthShiShenGan(),
      bazi.getMonthShiShenZhi(),
    );

    final dayPillar = _calculatePillar(
      bazi.getDay(),
      bazi.getDayGan(),
      bazi.getDayZhi(),
      bazi.getDayWuXing(),
      bazi.getDayNaYin(),
      bazi.getDayDiShi(),
      bazi.getDayHideGan(),
      bazi.getDayShiShenGan(),
      bazi.getDayShiShenZhi(),
    );

    final timePillar = _calculatePillar(
      bazi.getTime(),
      bazi.getTimeGan(),
      bazi.getTimeZhi(),
      bazi.getTimeWuXing(),
      bazi.getTimeNaYin(),
      bazi.getTimeDiShi(),
      bazi.getTimeHideGan(),
      bazi.getTimeShiShenGan(),
      bazi.getTimeShiShenZhi(),
    );

    // 计算运势
    final luck = _calculateLuck(yun);

    // 获取生肖和星座
    final zodiac = _getZodiac(solar.getYear());
    final constellation = _getConstellation(solar.getMonth(), solar.getDay());

    // 格式化日期
    final lunarDate = '${lunar.getYear()}年${lunar.getMonthInChinese()}月${lunar.getDayInChinese()} ${bazi.getTimeZhi()}时';
    final solarDate = DateFormat('yyyy年MM月dd日 HH:mm').format(birthTime);

    return BaziChart(
      year: yearPillar,
      month: monthPillar,
      day: dayPillar,
      time: timePillar,
      luck: luck,
      zodiac: zodiac,
      constellation: constellation,
      lunarDate: lunarDate,
      solarDate: solarDate,
    );
  }

  /// 计算单个柱
  static BaziPillar _calculatePillar(
    String ganzhi,
    String tiangan,
    String dizhi,
    String wuxing,
    String nayin,
    String dishi,
    List<String> canggan,
    String zhuxing,
    List<String> fuxing,
  ) {
    return BaziPillar(
      tiangan: tiangan,
      dizhi: dizhi,
      wuxing: wuxing,
      nayin: nayin,
      dishi: dishi,
      canggan: canggan,
      zhuxing: zhuxing,
      fuxing: fuxing,
    );
  }

  /// 计算运势
  static BaziLuck _calculateLuck(Yun yun) {
    final dayunList = yun.getDaYun();
    final baziDayunList = dayunList.map((dayun) {
      return BaziDayun(
        ganzhi: dayun.getGanZhi(),
        startAge: dayun.getStartAge(),
        endAge: dayun.getEndAge(),
        startYear: dayun.getStartYear().toString(),
        endYear: dayun.getEndYear().toString(),
      );
    }).toList();

    return BaziLuck(
      startYear: yun.getStartYear(),
      startMonth: yun.getStartMonth(),
      startDay: yun.getStartDay(),
      startHour: yun.getStartHour(),
      startSolar: yun.getStartSolar().toYmd(),
      dayun: baziDayunList,
    );
  }

  /// 获取生肖
  static String _getZodiac(int year) {
    const animals = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊'];
    return animals[year % 12];
  }



  /// 获取星座
  static String _getConstellation(int month, int day) {
    // 星座分界日期（每个星座的结束日期）
    const constellationDates = [
      [1, 19],   // 摩羯座 12/22 - 1/19
      [2, 18],   // 水瓶座 1/20 - 2/18
      [3, 20],   // 双鱼座 2/19 - 3/20
      [4, 19],   // 白羊座 3/21 - 4/19
      [5, 20],   // 金牛座 4/20 - 5/20
      [6, 21],   // 双子座 5/21 - 6/21
      [7, 22],   // 巨蟹座 6/22 - 7/22
      [8, 22],   // 狮子座 7/23 - 8/22
      [9, 22],   // 处女座 8/23 - 9/22
      [10, 23],  // 天秤座 9/23 - 10/23
      [11, 22],  // 天蝎座 10/24 - 11/22
      [12, 21],  // 射手座 11/23 - 12/21
    ];

    const constellations = [
      '摩羯座', '水瓶座', '双鱼座', '白羊座', '金牛座', '双子座',
      '巨蟹座', '狮子座', '处女座', '天秤座', '天蝎座', '射手座'
    ];

    // 根据月份和日期确定星座
    for (int i = 0; i < 12; i++) {
      final endMonth = constellationDates[i][0];
      final endDay = constellationDates[i][1];

      if (month < endMonth || (month == endMonth && day <= endDay)) {
        return constellations[i];
      }
    }

    // 如果都不匹配，说明是12月22日之后，属于摩羯座
    return constellations[0];
  }

  /// 获取五行属性
  static String getWuxing(String char, {bool isDizhi = false}) {
    if (isDizhi) {
      final index = BaziConstants.dizhi.indexOf(char);
      return index >= 0 ? BaziConstants.dizhiWuxing[index] : '*';
    } else {
      final index = BaziConstants.tiangan.indexOf(char);
      return index >= 0 ? BaziConstants.wuxing[index] : '*';
    }
  }

  /// 获取十神
  static String getShishen(String selfGan, String targetGan) {
    final shishenMap = BaziConstants.shishenMap[selfGan];
    if (shishenMap == null) return '*';
    return shishenMap[targetGan] ?? '*';
  }

  /// 转换十神简称
  static String transformShishen(String a, String b) {
    final shortA = BaziConstants.shishenShort[a] ?? a;
    final shortB = BaziConstants.shishenShort[b] ?? b;
    return '$shortA$shortB';
  }

  /// 生成八字分析
  static BaziAnalysis generateAnalysis(BaziChart chart, int gender) {
    // 这里可以根据八字信息生成基础分析
    // 实际项目中可以结合AI服务生成更详细的分析
    
    final wuxingAnalysis = _analyzeWuxing(chart);
    final personalityAnalysis = _analyzePersonality(chart, gender);
    final careerAnalysis = _analyzeCareer(chart);
    final loveAnalysis = _analyzeLove(chart, gender);
    final healthAnalysis = _analyzeHealth(chart);
    final luckAnalysis = _analyzeLuck(chart);

    return BaziAnalysis(
      wuxingAnalysis: wuxingAnalysis,
      personalityAnalysis: personalityAnalysis,
      careerAnalysis: careerAnalysis,
      loveAnalysis: loveAnalysis,
      healthAnalysis: healthAnalysis,
      luckAnalysis: luckAnalysis,
    );
  }

  static String _analyzeWuxing(BaziChart chart) {
    final wuxingCount = <String, int>{};
    
    // 统计五行
    [chart.year.wuxing, chart.month.wuxing, chart.day.wuxing, chart.time.wuxing]
        .forEach((wuxing) {
      wuxingCount[wuxing] = (wuxingCount[wuxing] ?? 0) + 1;
    });

    final strong = wuxingCount.entries
        .where((e) => e.value >= 2)
        .map((e) => e.key)
        .toList();
    
    final weak = ['木', '火', '土', '金', '水']
        .where((w) => !wuxingCount.containsKey(w))
        .toList();

    String analysis = '五行分析：\n';
    if (strong.isNotEmpty) {
      analysis += '五行偏强：${strong.join('、')}\n';
    }
    if (weak.isNotEmpty) {
      analysis += '五行偏弱：${weak.join('、')}\n';
    }
    
    return analysis;
  }

  static String _analyzePersonality(BaziChart chart, int gender) {
    return '性格分析：根据您的八字，您具有${chart.day.tiangan}日主的特质，'
           '${gender == 1 ? '男性' : '女性'}特征明显。';
  }

  static String _analyzeCareer(BaziChart chart) {
    return '事业分析：您的事业运势与${chart.day.wuxing}行相关，'
           '适合从事与此五行相关的行业。';
  }

  static String _analyzeLove(BaziChart chart, int gender) {
    return '感情分析：您的感情运势受${chart.month.tiangan}月干影响，'
           '${gender == 1 ? '财星' : '官星'}运势较为重要。';
  }

  static String _analyzeHealth(BaziChart chart) {
    return '健康分析：注意与${chart.day.wuxing}行相关的身体部位，'
           '保持五行平衡有助于身体健康。';
  }

  static String _analyzeLuck(BaziChart chart) {
    return '运势分析：您的大运从${chart.luck.startLabel}开始，'
           '每十年一个大运周期。';
  }
}
