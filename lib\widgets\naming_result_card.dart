import 'package:flutter/material.dart';
import '../models/naming_model.dart';

class NamingResultCard extends StatelessWidget {
  final NamingResult result;
  final VoidCallback? onSave;

  const NamingResultCard({
    super.key,
    required this.result,
    this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shadowColor: const Color(0xFFE5E5E5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: const BorderSide(color: Color(0xFFE5E5E5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 姓名显示
            Row(
              children: [
                Expanded(
                  child: _buildNameDisplay(),
                ),
                // 保存按钮
                if (onSave != null)
                  IconButton(
                    onPressed: onSave,
                    icon: const Icon(
                      Icons.bookmark_border,
                      color: Color(0xFF666666),
                      size: 20,
                    ),
                    tooltip: '保存到历史',
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // 诗句显示
            _buildSentenceDisplay(),
            
            const SizedBox(height: 12),
            
            // 出处信息
            _buildSourceInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildNameDisplay() {
    return Row(
      children: [
        // 姓氏
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            result.surname,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1A1A),
            ),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // 名字字符
        ...result.name.map((char) => Container(
          margin: const EdgeInsets.only(right: 4),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFF1A1A1A),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            char,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        )).toList(),
      ],
    );
  }

  Widget _buildSentenceDisplay() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: const Color(0xFFE5E5E5)),
      ),
      child: Wrap(
        children: [
          const Text(
            '[',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
            ),
          ),
          ...result.sentence.map((char) {
            final isInName = result.isCharInName(char);
            return Text(
              char,
              style: TextStyle(
                fontSize: 14,
                color: isInName ? const Color(0xFF1A1A1A) : const Color(0xFF666666),
                fontWeight: isInName ? FontWeight.w600 : FontWeight.normal,
              ),
            );
          }).toList(),
          const Text(
            ']',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSourceInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 诗词标题和作者
        Row(
          children: [
            Expanded(
              child: Text(
                '${result.book} • ${result.title}',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1A1A1A),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 4),
        
        // 朝代和作者
        Text(
          '[${result.dynasty}] ${result.author}',
          style: const TextStyle(
            fontSize: 11,
            color: Color(0xFF666666),
          ),
        ),
        
        const SizedBox(height: 4),
        
        // 诗词类型标签
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: const Color(0xFF1A1A1A),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            result.poetryType.displayName,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
