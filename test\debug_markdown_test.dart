import 'package:flutter_test/flutter_test.dart';
import 'package:daotianji/utils/markdown_cleaner.dart';

void main() {
  test('调试Markdown清理', () {
    const testText = '**这是粗体文本**';
    final result = MarkdownCleaner.clean(testText);
    print('输入: $testText');
    print('输出: $result');
    print('包含**?: ${result.contains('**')}');
    print('包含*?: ${result.contains('*')}');
    
    const testText2 = '***';
    final result2 = MarkdownCleaner.clean(testText2);
    print('输入2: $testText2');
    print('输出2: $result2');
    
    const testText3 = '这是标题';
    final result3 = MarkdownCleaner.clean(testText3);
    print('输入3: $testText3');
    print('输出3: $result3');
    print('包含"这是标题"?: ${result3.contains('这是标题')}');
  });
}
