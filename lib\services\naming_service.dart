import 'dart:math';
import '../models/naming_model.dart';
import '../data/poetry_data.dart';

/// 起名服务 - 基于 createName-master 项目的逻辑
class NamingService {
  static final Random _random = Random();

  /// 验证姓氏格式
  bool isValidSurname(String surname) {
    if (surname.isEmpty || surname.length > 2) return false;
    final regex = RegExp(r'^[\u4e00-\u9fa5]+$');
    return regex.hasMatch(surname);
  }

  /// 验证姓名格式
  bool isValidFullName(String fullName) {
    if (fullName.isEmpty || fullName.length < 2 || fullName.length > 4) return false;
    final regex = RegExp(r'^[\u4e00-\u9fa5]+$');
    return regex.hasMatch(fullName);
  }

  /// 生成名字 - 按照原项目逻辑
  Future<List<NamingResult>> generateNames({
    required String surname,
    required PoetryType poetryType,
    int count = 6,
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    final poetryList = PoetryData.getPoetryByType(poetryType);
    final results = <NamingResult>[];

    // 随机选择诗词（模拟云函数的 sample 操作）
    final selectedPoetries = _selectRandomPoetries(poetryList, count);

    for (final poetry in selectedPoetries) {
      // 按照原项目的处理逻辑
      var processedContent = poetry.content;

      // 1. 移除空格、换行、HTML标签等
      processedContent = processedContent.replaceAll(RegExp(r'\s|<br>|<p>|<\/p>|　|"|"'), '');

      // 2. 移除括号内容
      processedContent = processedContent.replaceAll(RegExp(r'\(.+\)'), '');

      // 3. 将句号、问号、感叹号、分号替换为分隔符
      processedContent = processedContent.replaceAll(RegExp(r'！|。|？|；'), '|');

      // 4. 移除末尾的分隔符
      processedContent = processedContent.replaceAll(RegExp(r'\|$'), '');

      // 5. 按分隔符分割成句子
      final sentences = processedContent.split('|').where((s) => s.length >= 2).toList();

      if (sentences.isNotEmpty) {
        // 6. 随机选择一个句子
        final selectedSentence = sentences[_randBetween(0, sentences.length)];

        // 7. 清理标点符号
        final cleanSentence = _cleanPunctuation(selectedSentence);

        // 8. 从句子中随机选择2个字符作为名字
        final name = _randCharFromStr(cleanSentence, 2, true);

        if (name.isNotEmpty) {
          results.add(NamingResult(
            surname: surname,
            givenName: name,
            fullName: surname + name,
            poetry: poetry,
            selectedChars: name.split(''),
            sentence: selectedSentence.split(''), // 保存完整句子用于高亮显示
            createdAt: DateTime.now(),
          ));
        }
      }
    }

    return results;
  }

  /// 随机选择诗词（模拟数据库的 sample 操作）
  List<Poetry> _selectRandomPoetries(List<Poetry> poetries, int count) {
    final shuffled = List<Poetry>.from(poetries)..shuffle(_random);
    return shuffled.take(count).toList();
  }

  /// 生成随机数 [min, max)
  int _randBetween(int min, int max) {
    return min + _random.nextInt(max - min);
  }

  /// 清理标点符号 - 按照原项目逻辑
  String _cleanPunctuation(String str) {
    final puncReg = RegExp(r'[<>《》！*\(\^\)\$%~!@#…&%￥—\+=、。，？；''""：·`]');
    return str.replaceAll(puncReg, '');
  }

  /// 从字符串中随机取字符 - 按照原项目逻辑
  String _randCharFromStr(String str, int num, bool ordered) {
    if (str.isEmpty) return '';

    final randNumArr = _genRandNumArr(str.length, num);

    if (ordered) {
      randNumArr.sort();
    }

    String result = '';
    for (final index in randNumArr) {
      result += str[index];
    }

    return result;
  }

  /// 生成随机数组 - 按照原项目逻辑
  List<int> _genRandNumArr(int max, int num) {
    if (num > max) {
      num = max;
    }

    final orderedNum = List.generate(max, (index) => index);
    final result = <int>[];

    for (int i = 0; i < num; i++) {
      final randIndex = _randBetween(0, orderedNum.length);
      final randNum = orderedNum[randIndex];
      result.add(randNum);
      orderedNum.removeAt(randIndex);
    }

    return result;
  }
}
