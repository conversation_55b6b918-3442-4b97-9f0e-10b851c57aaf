import 'dart:math';
import '../models/naming_model.dart';
import '../data/poetry_data.dart';

class NamingService {
  final Random _random = Random();

  /// 根据姓氏和诗词类型生成名字
  Future<List<NamingResult>> generateNames({
    required String surname,
    required PoetryType poetryType,
    int count = 6,
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    final poetryList = _getPoetryByType(poetryType);
    if (poetryList.isEmpty) {
      throw Exception('没有找到对应的诗词数据');
    }

    final results = <NamingResult>[];
    final usedPoetries = <Poetry>{};

    for (int i = 0; i < count && usedPoetries.length < poetryList.length; i++) {
      Poetry poetry;
      do {
        poetry = poetryList[_random.nextInt(poetryList.length)];
      } while (usedPoetries.contains(poetry));
      
      usedPoetries.add(poetry);

      try {
        final namingResult = _generateNameFromPoetry(
          surname: surname,
          poetry: poetry,
          poetryType: poetryType,
        );
        if (namingResult != null) {
          results.add(namingResult);
        }
      } catch (e) {
        // 如果某首诗词生成失败，继续尝试下一首
        continue;
      }
    }

    if (results.isEmpty) {
      throw Exception('无法从选定的诗词类型中生成合适的名字');
    }

    return results;
  }

  /// 从诗词中生成名字
  NamingResult? _generateNameFromPoetry({
    required String surname,
    required Poetry poetry,
    required PoetryType poetryType,
  }) {
    // 清理诗词内容
    String cleanContent = _cleanPoetryContent(poetry.content);
    
    // 分割成句子
    List<String> sentences = _splitIntoSentences(cleanContent);
    if (sentences.isEmpty) return null;

    // 选择一个句子
    String selectedSentence = sentences[_random.nextInt(sentences.length)];
    if (selectedSentence.length < 2) return null;

    // 从句子中随机选择两个字作为名字
    List<String> nameChars = _selectRandomChars(selectedSentence, 2);
    if (nameChars.length < 2) return null;

    return NamingResult(
      surname: surname,
      name: nameChars,
      sentence: selectedSentence.split(''),
      title: poetry.title,
      author: poetry.author ?? '佚名',
      book: poetry.book,
      dynasty: poetry.dynasty,
      poetryType: poetryType,
      createdAt: DateTime.now(),
    );
  }

  /// 清理诗词内容，移除标点符号等
  String _cleanPoetryContent(String content) {
    // 移除常见的标点符号和特殊字符
    final punctuationRegex = RegExp(r'[<>《》！*\(\^\)\$%~!@#…&%￥—\+=、。，？；''""：·`\s|<br>|<p>|</p>|　|"|"]');
    String cleaned = content.replaceAll(punctuationRegex, '');
    
    // 移除括号内容
    cleaned = cleaned.replaceAll(RegExp(r'\(.+\)'), '');
    
    return cleaned;
  }

  /// 将内容分割成句子
  List<String> _splitIntoSentences(String content) {
    // 先按照句号、问号、感叹号等分割
    String processed = content.replaceAll(RegExp(r'[！。？；]'), '|');
    processed = processed.replaceAll(RegExp(r'\|$'), ''); // 移除末尾的分隔符
    
    List<String> sentences = processed.split('|')
        .where((sentence) => sentence.length >= 2)
        .toList();
    
    return sentences;
  }

  /// 从字符串中随机选择指定数量的字符
  List<String> _selectRandomChars(String text, int count) {
    if (text.length < count) return [];
    
    List<int> indices = _generateRandomIndices(text.length, count);
    indices.sort(); // 保持顺序
    
    return indices.map((index) => text[index]).toList();
  }

  /// 生成随机索引数组
  List<int> _generateRandomIndices(int maxLength, int count) {
    if (count > maxLength) count = maxLength;
    
    List<int> allIndices = List.generate(maxLength, (index) => index);
    List<int> selectedIndices = [];
    
    for (int i = 0; i < count; i++) {
      int randomIndex = _random.nextInt(allIndices.length);
      selectedIndices.add(allIndices[randomIndex]);
      allIndices.removeAt(randomIndex);
    }
    
    return selectedIndices;
  }

  /// 根据诗词类型获取对应的诗词列表
  List<Poetry> _getPoetryByType(PoetryType type) {
    switch (type) {
      case PoetryType.tangshi:
        return PoetryData.tangshi;
      case PoetryType.songci:
        return PoetryData.songci;
      case PoetryType.shijing:
        return PoetryData.shijing;
      case PoetryType.chuci:
        return PoetryData.chuci;
      case PoetryType.yuefu:
        return PoetryData.yuefu;
      case PoetryType.gushi:
        return PoetryData.gushi;
    }
  }

  /// 验证姓氏是否有效
  bool isValidSurname(String surname) {
    if (surname.isEmpty || surname.length > 2) return false;
    
    // 检查是否为中文字符
    final chineseRegex = RegExp(r'^[\u4E00-\u9FA5]+$');
    return chineseRegex.hasMatch(surname);
  }

  /// 验证姓名是否有效
  bool isValidFullName(String fullName) {
    if (fullName.isEmpty || fullName.length > 4) return false;
    
    // 检查是否为中文字符
    final chineseRegex = RegExp(r'^[\u4E00-\u9FA5]+$');
    return chineseRegex.hasMatch(fullName);
  }
}
