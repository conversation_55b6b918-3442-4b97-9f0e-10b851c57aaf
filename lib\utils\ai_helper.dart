import '../services/global_ai_service.dart';
import '../models/huang_li_model.dart';
import '../config/app_config.dart';

/// AI助手工具类，提供各种AI功能的便捷调用
class AIHelper {
  static final GlobalAIService _aiService = GlobalAIService();

  /// 黄历解读助手
  static Future<String> consultHuangLi({
    required String question,
    required HuangLiData huangLiData,
    String? specificTime,
    String? activity,
  }) async {
    return await _aiService.getHuangLiAdvice(
      question: question,
      huangLiData: huangLiData,
      specificTime: specificTime,
      activity: activity,
    );
  }

  /// 黄历解读助手（流式）
  static Stream<String> consultHuangLiStream({
    required String question,
    required HuangLiData huangLiData,
    String? specificTime,
    String? activity,
  }) {
    return _aiService.getHuangLiAdviceStream(
      question: question,
      huangLiData: huangLiData,
      specificTime: specificTime,
      activity: activity,
    );
  }

  /// 通用AI对话助手
  static Future<String> chat({
    required String systemPrompt,
    required String userMessage,
  }) async {
    return await _aiService.chat(
      systemPrompt: systemPrompt,
      userMessage: userMessage,
    );
  }

  /// 通用AI对话助手（流式）
  static Stream<String> chatStream({
    required String systemPrompt,
    required String userMessage,
  }) {
    return _aiService.chatStream(
      systemPrompt: systemPrompt,
      userMessage: userMessage,
    );
  }

  /// 六爻解读助手
  static Future<String> interpretDivination({
    required String question,
    required String gua,
    required String guaName,
    required String guaDes,
    required String guaSentence,
  }) async {
    return await _aiService.chat(
      systemPrompt: AppConfig.systemPrompt,
      userMessage: '''
问题是：$question,
六爻结果是：$gua,
卦名为：$guaName,
$guaDes,
卦辞为：$guaSentence''',
    );
  }

  /// 六爻解读助手（流式）
  static Stream<String> interpretDivinationStream({
    required String question,
    required String gua,
    required String guaName,
    required String guaDes,
    required String guaSentence,
  }) {
    return _aiService.chatStream(
      systemPrompt: AppConfig.systemPrompt,
      userMessage: '''
问题是：$question,
六爻结果是：$gua,
卦名为：$guaName,
$guaDes,
卦辞为：$guaSentence''',
    );
  }

  /// 九型人格分析助手
  static Future<String> analyzePersonality({
    required Map<String, int> scores,
    required String primaryType,
  }) async {
    const systemPrompt = '''
你是一位专业的九型人格分析师，拥有深厚的心理学知识和丰富的人格分析经验。

📋 输出格式要求：
• 不要使用Markdown格式符号（如*、#、-、**等）
• 可以使用emoji表情符号来美化内容
• 要有清晰的分段和排版，类似Word文档格式
• 每个分析部分之间要有空行分隔
• 重要内容可以用emoji标注

🔍 请按以下结构进行分析：

🎯 人格类型概述
简要介绍该人格类型的核心特征

💫 核心动机与恐惧
深入分析内在驱动力和深层恐惧

🌟 性格优势
详细说明该类型的积极特质和天赋

⚠️ 成长挑战
指出需要注意和改善的方面

💼 职业发展建议
推荐适合的职业方向和工作环境

💕 人际关系指导
分析在人际交往中的特点和建议

🌱 个人成长方向
提供具体的自我提升建议

你的任务是根据用户的九型人格测试结果，为他们提供深入的分析和建议。你的回答应该专业、准确、有建设性，帮助用户更好地了解自己。内容要详细完整，排版要清晰美观。
''';

    final scoresText = scores.entries
        .map((entry) => '${entry.key}: ${entry.value}分')
        .join('、');

    return await _aiService.chat(
      systemPrompt: systemPrompt,
      userMessage: '''
用户的九型人格测试结果如下：
主要类型：$primaryType
各类型得分：$scoresText

请为用户提供详细的人格分析，包括：
1. 主要人格特征
2. 优势和潜力
3. 可能的挑战和盲点
4. 个人成长建议
5. 人际关系建议
''',
    );
  }

  /// 自定义AI助手
  /// 可以用于其他功能模块的AI需求
  static Future<String> customAI({
    required String role,
    required String task,
    required String context,
  }) async {
    final systemPrompt = '''
你是一位$role，你的任务是$task。

📋 输出格式要求：
• 不要使用Markdown格式符号（如*、#、-、**等）
• 可以使用emoji表情符号来美化内容
• 要有清晰的分段和排版，类似Word文档格式
• 每个分析部分之间要有空行分隔
• 重要内容可以用emoji标注

请根据提供的信息，给出专业、准确、有用的回答。内容要详细完整，排版要清晰美观。
''';

    return await _aiService.chat(
      systemPrompt: systemPrompt,
      userMessage: context,
    );
  }

  /// 自定义AI助手（流式）
  static Stream<String> customAIStream({
    required String role,
    required String task,
    required String context,
  }) {
    final systemPrompt = '''
你是一位$role，你的任务是$task。

📋 输出格式要求：
• 不要使用Markdown格式符号（如*、#、-、**等）
• 可以使用emoji表情符号来美化内容
• 要有清晰的分段和排版，类似Word文档格式
• 每个分析部分之间要有空行分隔
• 重要内容可以用emoji标注

请根据提供的信息，给出专业、准确、有用的回答。内容要详细完整，排版要清晰美观。
''';

    return _aiService.chatStream(
      systemPrompt: systemPrompt,
      userMessage: context,
    );
  }
}

/// AI功能使用示例
class AIUsageExamples {
  /// 黄历咨询示例
  static Future<void> huangLiExample() async {
    final huangLiData = HuangLiData.fromDate(DateTime.now());
    
    try {
      final result = await AIHelper.consultHuangLi(
        question: '今天下午2点适合洗澡吗？',
        huangLiData: huangLiData,
        specificTime: '下午2点',
        activity: '洗澡',
      );
      print('黄历AI回复: $result');
    } catch (e) {
      print('黄历咨询失败: $e');
    }
  }

  /// 六爻解读示例
  static Future<void> divinationExample() async {
    try {
      final result = await AIHelper.interpretDivination(
        question: '我的事业发展如何？',
        gua: '111010',
        guaName: '乾为天',
        guaDes: '乾卦代表天，象征刚健、进取',
        guaSentence: '元亨利贞',
      );
      print('六爻解读: $result');
    } catch (e) {
      print('六爻解读失败: $e');
    }
  }

  /// 自定义AI示例
  static Future<void> customAIExample() async {
    try {
      final result = await AIHelper.customAI(
        role: '风水师',
        task: '分析家居风水布局',
        context: '我的客厅朝南，沙发靠墙放置，电视在对面墙上，请分析这个布局的风水如何？',
      );
      print('风水分析: $result');
    } catch (e) {
      print('风水分析失败: $e');
    }
  }
}
