// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bazi_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaziModel _$BaziModelFromJson(Map<String, dynamic> json) => BaziModel(
      name: json['name'] as String,
      gender: (json['gender'] as num).toInt(),
      birthTime: DateTime.parse(json['birthTime'] as String),
      chart: BaziChart.fromJson(json['chart'] as Map<String, dynamic>),
      analysis: BaziAnalysis.fromJson(json['analysis'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$BaziModelToJson(BaziModel instance) => <String, dynamic>{
      'name': instance.name,
      'gender': instance.gender,
      'birthTime': instance.birthTime.toIso8601String(),
      'chart': instance.chart,
      'analysis': instance.analysis,
      'createdAt': instance.createdAt.toIso8601String(),
    };

BaziChart _$BaziChartFromJson(Map<String, dynamic> json) => BaziChart(
      year: BaziPillar.fromJson(json['year'] as Map<String, dynamic>),
      month: BaziPillar.fromJson(json['month'] as Map<String, dynamic>),
      day: BaziPillar.fromJson(json['day'] as Map<String, dynamic>),
      time: BaziPillar.fromJson(json['time'] as Map<String, dynamic>),
      luck: BaziLuck.fromJson(json['luck'] as Map<String, dynamic>),
      zodiac: json['zodiac'] as String,
      constellation: json['constellation'] as String,
      lunarDate: json['lunarDate'] as String,
      solarDate: json['solarDate'] as String,
    );

Map<String, dynamic> _$BaziChartToJson(BaziChart instance) => <String, dynamic>{
      'year': instance.year,
      'month': instance.month,
      'day': instance.day,
      'time': instance.time,
      'luck': instance.luck,
      'zodiac': instance.zodiac,
      'constellation': instance.constellation,
      'lunarDate': instance.lunarDate,
      'solarDate': instance.solarDate,
    };

BaziPillar _$BaziPillarFromJson(Map<String, dynamic> json) => BaziPillar(
      tiangan: json['tiangan'] as String,
      dizhi: json['dizhi'] as String,
      wuxing: json['wuxing'] as String,
      nayin: json['nayin'] as String,
      dishi: json['dishi'] as String,
      canggan:
          (json['canggan'] as List<dynamic>).map((e) => e as String).toList(),
      zhuxing: json['zhuxing'] as String,
      fuxing:
          (json['fuxing'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$BaziPillarToJson(BaziPillar instance) =>
    <String, dynamic>{
      'tiangan': instance.tiangan,
      'dizhi': instance.dizhi,
      'wuxing': instance.wuxing,
      'nayin': instance.nayin,
      'dishi': instance.dishi,
      'canggan': instance.canggan,
      'zhuxing': instance.zhuxing,
      'fuxing': instance.fuxing,
    };

BaziLuck _$BaziLuckFromJson(Map<String, dynamic> json) => BaziLuck(
      startYear: (json['startYear'] as num).toInt(),
      startMonth: (json['startMonth'] as num).toInt(),
      startDay: (json['startDay'] as num).toInt(),
      startHour: (json['startHour'] as num).toInt(),
      startSolar: json['startSolar'] as String,
      dayun: (json['dayun'] as List<dynamic>)
          .map((e) => BaziDayun.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BaziLuckToJson(BaziLuck instance) => <String, dynamic>{
      'startYear': instance.startYear,
      'startMonth': instance.startMonth,
      'startDay': instance.startDay,
      'startHour': instance.startHour,
      'startSolar': instance.startSolar,
      'dayun': instance.dayun,
    };

BaziDayun _$BaziDayunFromJson(Map<String, dynamic> json) => BaziDayun(
      ganzhi: json['ganzhi'] as String,
      startAge: (json['startAge'] as num).toInt(),
      endAge: (json['endAge'] as num).toInt(),
      startYear: json['startYear'] as String,
      endYear: json['endYear'] as String,
    );

Map<String, dynamic> _$BaziDayunToJson(BaziDayun instance) => <String, dynamic>{
      'ganzhi': instance.ganzhi,
      'startAge': instance.startAge,
      'endAge': instance.endAge,
      'startYear': instance.startYear,
      'endYear': instance.endYear,
    };

BaziAnalysis _$BaziAnalysisFromJson(Map<String, dynamic> json) => BaziAnalysis(
      wuxingAnalysis: json['wuxingAnalysis'] as String,
      personalityAnalysis: json['personalityAnalysis'] as String,
      careerAnalysis: json['careerAnalysis'] as String,
      loveAnalysis: json['loveAnalysis'] as String,
      healthAnalysis: json['healthAnalysis'] as String,
      luckAnalysis: json['luckAnalysis'] as String,
    );

Map<String, dynamic> _$BaziAnalysisToJson(BaziAnalysis instance) =>
    <String, dynamic>{
      'wuxingAnalysis': instance.wuxingAnalysis,
      'personalityAnalysis': instance.personalityAnalysis,
      'careerAnalysis': instance.careerAnalysis,
      'loveAnalysis': instance.loveAnalysis,
      'healthAnalysis': instance.healthAnalysis,
      'luckAnalysis': instance.luckAnalysis,
    };
