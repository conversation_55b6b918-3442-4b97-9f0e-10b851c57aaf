import 'package:flutter_test/flutter_test.dart';
import 'package:daotianji/utils/markdown_cleaner.dart';

void main() {
  group('AI输出格式测试', () {
    test('应该保留emoji符号', () {
      const testText = '''
🎯 卦象总述
这是一个包含emoji的文本 😊

📊 详细分析
**重要内容**需要保留emoji 🌟

💡 具体建议
- 第一条建议 ✅
- 第二条建议 ❌
''';

      final cleanedText = MarkdownCleaner.gentleClean(testText);
      
      // 验证emoji被保留
      expect(cleanedText.contains('🎯'), true);
      expect(cleanedText.contains('😊'), true);
      expect(cleanedText.contains('📊'), true);
      expect(cleanedText.contains('🌟'), true);
      expect(cleanedText.contains('💡'), true);
      expect(cleanedText.contains('✅'), true);
      expect(cleanedText.contains('❌'), true);
      
      // 验证Markdown符号被移除
      expect(cleanedText.contains('**'), false);
      expect(cleanedText.contains('- '), false);
      
      // 验证文本内容保留
      expect(cleanedText.contains('卦象总述'), true);
      expect(cleanedText.contains('详细分析'), true);
      expect(cleanedText.contains('具体建议'), true);
      expect(cleanedText.contains('重要内容'), true);
    });

    test('应该检测emoji存在', () {
      expect(MarkdownCleaner.containsEmoji('这是普通文本'), false);
      expect(MarkdownCleaner.containsEmoji('这是包含emoji的文本 😊'), true);
      expect(MarkdownCleaner.containsEmoji('🎯 目标'), true);
      expect(MarkdownCleaner.containsEmoji('📊 数据分析'), true);
      expect(MarkdownCleaner.containsEmoji('💡 建议'), true);
      expect(MarkdownCleaner.containsEmoji('⚠️ 警告'), true);
    });

    test('应该正确处理混合格式', () {
      const testText = '''
🌟 基本命格分析
您的八字组合显示出**强烈的领导特质**

⚖️ 五行强弱分析  
五行分布如下：
- 木：偏强 🌳
- 火：适中 🔥
- 土：偏弱 🏔️

💼 事业财运分析
*适合的职业*包括：
1. 管理岗位
2. 创业方向
''';

      final cleanedText = MarkdownCleaner.gentleClean(testText);
      
      // 验证emoji保留
      expect(cleanedText.contains('🌟'), true);
      expect(cleanedText.contains('⚖️'), true);
      expect(cleanedText.contains('🌳'), true);
      expect(cleanedText.contains('🔥'), true);
      expect(cleanedText.contains('🏔️'), true);
      expect(cleanedText.contains('💼'), true);
      
      // 验证内容保留
      expect(cleanedText.contains('基本命格分析'), true);
      expect(cleanedText.contains('强烈的领导特质'), true);
      expect(cleanedText.contains('五行强弱分析'), true);
      expect(cleanedText.contains('事业财运分析'), true);
      expect(cleanedText.contains('适合的职业'), true);
    });

    test('应该处理纯文本（无emoji）', () {
      const testText = '''
# 标题
**粗体文本**
*斜体文本*
- 列表项
1. 数字列表
''';

      final cleanedText = MarkdownCleaner.gentleClean(testText);
      
      // 由于没有emoji，应该使用标准清理
      expect(cleanedText.contains('#'), false);
      expect(cleanedText.contains('**'), false);
      expect(cleanedText.contains('*'), false);
      expect(cleanedText.contains('- '), false);
      expect(cleanedText.contains('1. '), false);
      
      // 内容应该保留
      expect(cleanedText.contains('标题'), true);
      expect(cleanedText.contains('粗体文本'), true);
      expect(cleanedText.contains('斜体文本'), true);
      expect(cleanedText.contains('列表项'), true);
      expect(cleanedText.contains('数字列表'), true);
    });

    test('应该保持良好的排版格式', () {
      const testText = '''
🎯 人格类型概述
您属于第一型人格

💫 核心动机与恐惧
内在驱动力很强

🌟 性格优势
具有以下优点：
- 完美主义 ✨
- 责任感强 💪

⚠️ 成长挑战
需要注意的方面
''';

      final cleanedText = MarkdownCleaner.gentleClean(testText);
      
      // 验证段落结构保留
      expect(cleanedText.split('\n').length, greaterThan(5));
      
      // 验证emoji和内容都保留
      expect(cleanedText.contains('🎯'), true);
      expect(cleanedText.contains('💫'), true);
      expect(cleanedText.contains('🌟'), true);
      expect(cleanedText.contains('⚠️'), true);
      expect(cleanedText.contains('✨'), true);
      expect(cleanedText.contains('💪'), true);
      
      expect(cleanedText.contains('人格类型概述'), true);
      expect(cleanedText.contains('核心动机与恐惧'), true);
      expect(cleanedText.contains('性格优势'), true);
      expect(cleanedText.contains('成长挑战'), true);
    });
  });
}
