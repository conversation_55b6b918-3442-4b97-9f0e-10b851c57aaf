import 'package:lunar/lunar.dart';

/// Tyme (Lunar) 日历工具类
/// 基于 6tail 的 lunar 库，提供强大的日历功能
class TymeHelper {
  /// 获取当前日期的农历信息
  static Lunar getCurrentLunar() {
    return Lunar.fromDate(DateTime.now());
  }

  /// 根据公历日期获取农历信息
  static Lunar getLunarFromDate(DateTime date) {
    return Lunar.fromDate(date);
  }

  /// 根据农历年月日获取农历信息
  static Lunar getLunarFromYmd(int year, int month, int day) {
    return Lunar.fromYmd(year, month, day);
  }

  /// 获取公历信息
  static Solar getSolarFromDate(DateTime date) {
    return Solar.fromDate(date);
  }

  /// 获取八字信息
  static EightChar getEightChar(DateTime date) {
    final lunar = Lunar.fromDate(date);
    return lunar.getEightChar();
  }

  /// 获取详细的黄历信息
  static Map<String, dynamic> getDetailedHuangLi(DateTime date) {
    final lunar = Lunar.fromDate(date);
    final solar = Solar.fromDate(date);
    final eightChar = lunar.getEightChar();

    return {
      // 基本日期信息
      'solar': {
        'date': solar.toYmd(),
        'fullString': solar.toFullString(),
        'weekday': solar.getWeek(),
        'weekdayInChinese': solar.getWeekInChinese(),
      },
      
      // 农历信息
      'lunar': {
        'date': '${lunar.getYear()}-${lunar.getMonth()}-${lunar.getDay()}',
        'fullString': lunar.toFullString(),
        'monthInChinese': lunar.getMonthInChinese(),
        'dayInChinese': lunar.getDayInChinese(),
        'yearInChinese': lunar.getYearInChinese(),
        'yearInGanZhi': lunar.getYearInGanZhi(),
        'monthInGanZhi': lunar.getMonthInGanZhi(),
        'dayInGanZhi': lunar.getDayInGanZhi(),
        'timeInGanZhi': lunar.getTimeInGanZhi(),
      },

      // 八字信息
      'eightChar': {
        'year': eightChar.getYear(),
        'month': eightChar.getMonth(),
        'day': eightChar.getDay(),
        'time': eightChar.getTime(),
        'yearGanZhi': eightChar.getYear(),
        'monthGanZhi': eightChar.getMonth(),
        'dayGanZhi': eightChar.getDay(),
        'timeGanZhi': eightChar.getTime(),
        'yearNaYin': eightChar.getYearNaYin(),
        'monthNaYin': eightChar.getMonthNaYin(),
        'dayNaYin': eightChar.getDayNaYin(),
        'timeNaYin': eightChar.getTimeNaYin(),
      },

      // 生肖和星座
      'zodiac': {
        'shengXiao': lunar.getYearShengXiao(),
        'constellation': solar.getXingZuo(),
      },

      // 节气信息
      'jieQi': {
        'current': lunar.getCurrentJieQi()?.getName(),
        'next': lunar.getNextJieQi()?.getName(),
        'currentDate': lunar.getCurrentJieQi()?.getSolar()?.toYmd(),
        'nextDate': lunar.getNextJieQi()?.getSolar()?.toYmd(),
      },

      // 宜忌信息
      'yiJi': {
        'yi': lunar.getDayYi(),
        'ji': lunar.getDayJi(),
      },

      // 神煞信息
      'shenSha': {
        'pengZuBaiJi': lunar.getPengZuGan() + ' ' + lunar.getPengZuZhi(),
        'chong': lunar.getDayChongDesc(),
        'sha': lunar.getDaySha(),
        'xingXiu': lunar.getXiu() + lunar.getXiuLuck(),
        'zhiShen': lunar.getZhiXing(),
      },

      // 吉神方位
      'jiShenFangWei': {
        'xiShen': lunar.getDayPositionXi(),
        'xiShenDesc': lunar.getDayPositionXiDesc(),
        'yangGui': lunar.getDayPositionYangGui(),
        'yangGuiDesc': lunar.getDayPositionYangGuiDesc(),
        'yinGui': lunar.getDayPositionYinGui(),
        'yinGuiDesc': lunar.getDayPositionYinGuiDesc(),
        'fuShen': lunar.getDayPositionFu(),
        'fuShenDesc': lunar.getDayPositionFuDesc(),
        'caiShen': lunar.getDayPositionCai(),
        'caiShenDesc': lunar.getDayPositionCaiDesc(),
      },

      // 胎神方位
      'taiShen': {
        'position': lunar.getDayPositionTai(),
        'desc': lunar.getDayPositionTai(),
      },

      // 建除十二值星
      'jianChu': {
        'value': lunar.getZhiXing(),
        'luck': lunar.getZhiXing(),
      },

      // 十二神
      'shiErShen': {
        'value': lunar.getDayTianShen(),
        'luck': lunar.getDayTianShenLuck(),
        'type': lunar.getDayTianShenType(),
      },

      // 黄道吉日
      'huangDaoJiRi': {
        'isHuangDao': lunar.getDayTianShenType() == '黄道',
        'desc': lunar.getDayTianShenType(),
      },

      // 节日信息
      'festivals': {
        'solar': solar.getFestivals(),
        'lunar': lunar.getFestivals(),
        'other': lunar.getOtherFestivals(),
      },
    };
  }

  /// 获取简化的黄历信息（用于日历显示）
  static Map<String, String> getSimpleHuangLi(DateTime date) {
    final lunar = Lunar.fromDate(date);
    final solar = Solar.fromDate(date);

    return {
      'solarDate': solar.toYmd(),
      'lunarDate': '${lunar.getMonthInChinese()}${lunar.getDayInChinese()}',
      'weekday': solar.getWeekInChinese(),
      'ganZhi': lunar.getDayInGanZhi(),
      'shengXiao': lunar.getYearShengXiao(),
      'constellation': solar.getXingZuo(),
      'jieQi': lunar.getCurrentJieQi()?.getName() ?? '',
      'yi': lunar.getDayYi().take(3).join(' '),
      'ji': lunar.getDayJi().take(3).join(' '),
      'chong': lunar.getDayChongDesc(),
      'sha': lunar.getDaySha(),
      'xingXiu': lunar.getXiu() + lunar.getXiuLuck(),
      'jiShen': lunar.getDayPositionXi(),
      'taiShen': lunar.getDayPositionTai(),
      'jianChu': lunar.getZhiXing(),
      'tianShen': lunar.getDayTianShen(),
      'huangDaoType': lunar.getDayTianShenType(),
    };
  }

  /// 获取月份的所有日期信息
  static List<Map<String, String>> getMonthDates(int year, int month) {
    final List<Map<String, String>> dates = [];
    final DateTime firstDay = DateTime(year, month, 1);
    final DateTime lastDay = DateTime(year, month + 1, 0);

    for (int day = 1; day <= lastDay.day; day++) {
      final date = DateTime(year, month, day);
      dates.add(getSimpleHuangLi(date));
    }

    return dates;
  }

  /// 判断是否为黄道吉日
  static bool isHuangDaoJiRi(DateTime date) {
    final lunar = Lunar.fromDate(date);
    return lunar.getDayTianShenType() == '黄道';
  }

  /// 获取当前节气
  static String getCurrentJieQi() {
    final lunar = getCurrentLunar();
    return lunar.getCurrentJieQi()?.getName() ?? '';
  }

  /// 获取下一个节气
  static String getNextJieQi() {
    final lunar = getCurrentLunar();
    return lunar.getNextJieQi()?.getName() ?? '';
  }

  /// 格式化农历日期显示
  static String formatLunarDate(Lunar lunar) {
    return '${lunar.getYearInChinese()}年${lunar.getMonthInChinese()}${lunar.getDayInChinese()}';
  }

  /// 格式化公历日期显示
  static String formatSolarDate(Solar solar) {
    return '${solar.getYear()}年${solar.getMonth()}月${solar.getDay()}日';
  }

  /// 获取生辰八字详细信息
  static Map<String, dynamic> getBaZiDetails(DateTime birthDate) {
    final lunar = Lunar.fromDate(birthDate);
    final eightChar = lunar.getEightChar();

    return {
      'birthDate': {
        'solar': formatSolarDate(Solar.fromDate(birthDate)),
        'lunar': formatLunarDate(lunar),
        'weekday': Solar.fromDate(birthDate).getWeekInChinese(),
      },
      'baZi': {
        'year': eightChar.getYear(),
        'month': eightChar.getMonth(),
        'day': eightChar.getDay(),
        'time': eightChar.getTime(),
      },
      'naYin': {
        'year': eightChar.getYearNaYin(),
        'month': eightChar.getMonthNaYin(),
        'day': eightChar.getDayNaYin(),
        'time': eightChar.getTimeNaYin(),
      },
      'shengXiao': lunar.getYearShengXiao(),
      'constellation': Solar.fromDate(birthDate).getXingZuo(),
      'wuXing': {
        'year': eightChar.getYearWuXing(),
        'month': eightChar.getMonthWuXing(),
        'day': eightChar.getDayWuXing(),
        'time': eightChar.getTimeWuXing(),
      },
    };
  }
}
