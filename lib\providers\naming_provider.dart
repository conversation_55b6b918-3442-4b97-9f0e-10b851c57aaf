import 'package:flutter/foundation.dart';
import '../models/naming_model.dart';
import '../services/naming_service.dart';
import '../services/global_ai_service.dart';
import '../services/universal_history_service.dart';

class NamingProvider extends ChangeNotifier {
  final NamingService _namingService = NamingService();
  
  // 起名相关状态
  List<NamingResult> _namingResults = [];
  bool _isGeneratingNames = false;
  String? _namingError;
  String _currentSurname = '';
  PoetryType _selectedPoetryType = PoetryType.tangshi;
  
  // 测名相关状态
  String _currentAnalysisName = '';
  String _nameAnalysis = '';
  bool _isAnalyzingName = false;
  String? _analysisError;
  
  // Getters
  List<NamingResult> get namingResults => _namingResults;
  bool get isGeneratingNames => _isGeneratingNames;
  String? get namingError => _namingError;
  String get currentSurname => _currentSurname;
  PoetryType get selectedPoetryType => _selectedPoetryType;
  
  String get currentAnalysisName => _currentAnalysisName;
  String get nameAnalysis => _nameAnalysis;
  bool get isAnalyzingName => _isAnalyzingName;
  String? get analysisError => _analysisError;

  /// 设置姓氏
  void setSurname(String surname) {
    _currentSurname = surname;
    notifyListeners();
  }

  /// 设置诗词类型
  void setPoetryType(PoetryType type) {
    _selectedPoetryType = type;
    notifyListeners();
  }

  /// 生成名字
  Future<void> generateNames() async {
    if (_currentSurname.isEmpty) {
      _setNamingError('请输入姓氏');
      return;
    }

    if (!_namingService.isValidSurname(_currentSurname)) {
      _setNamingError('姓氏格式不正确，请输入1-2个中文字符');
      return;
    }

    _setGeneratingNames(true);
    _clearNamingError();

    try {
      final results = await _namingService.generateNames(
        surname: _currentSurname,
        poetryType: _selectedPoetryType,
        count: 6,
      );
      
      _namingResults = results;
      notifyListeners();
    } catch (e) {
      _setNamingError('生成名字失败: $e');
    } finally {
      _setGeneratingNames(false);
    }
  }

  /// 分析名字（测名）
  Future<void> analyzeName(String fullName) async {
    if (fullName.isEmpty) {
      _setAnalysisError('请输入要分析的姓名');
      return;
    }

    if (!_namingService.isValidFullName(fullName)) {
      _setAnalysisError('姓名格式不正确，请输入2-4个中文字符');
      return;
    }

    _currentAnalysisName = fullName;
    _setAnalyzingName(true);
    _clearAnalysisError();
    _nameAnalysis = '';
    notifyListeners();

    try {
      final systemPrompt = '''
你是一位专业的姓名学专家，精通传统文化和现代心理学。请对用户提供的姓名进行全面、客观的分析。
分析要专业但通俗易懂，给出具体的见解和建议。
请用温和、专业的语调进行分析，避免过于绝对的判断。
''';

      final userPrompt = '''
请对姓名"$fullName"进行详细的分析，包括以下方面：

1. 字义分析：分析每个字的含义和寓意
2. 音韵分析：分析名字的读音特点和音律美感
3. 五行分析：根据传统五行理论分析名字的五行属性
4. 文化内涵：分析名字的文化背景和典故
5. 性格暗示：根据名字特点分析可能的性格倾向
6. 吉凶评价：给出综合的吉凶评价和建议

请提供详细、全面的分析。
''';

      final stream = GlobalAIService().chatStream(
        systemPrompt: systemPrompt,
        userMessage: userPrompt,
      );

      await for (final chunk in stream) {
        _nameAnalysis += chunk;
        notifyListeners();
      }

      // 保存到历史记录
      await _saveAnalysisToHistory();
      
    } catch (e) {
      _setAnalysisError('分析失败: $e');
    } finally {
      _setAnalyzingName(false);
    }
  }

  /// 保存起名结果到历史记录
  Future<void> saveNamingResultToHistory(NamingResult result) async {
    try {
      // 暂时注释掉，等待UniversalRecord模型更新
      debugPrint('起名记录保存功能待实现');
    } catch (e) {
      debugPrint('保存起名记录失败: $e');
    }
  }

  /// 保存测名分析到历史记录
  Future<void> _saveAnalysisToHistory() async {
    if (_currentAnalysisName.isEmpty || _nameAnalysis.isEmpty) return;

    try {
      // 暂时注释掉，等待UniversalRecord模型更新
      debugPrint('测名记录保存功能待实现');
    } catch (e) {
      debugPrint('保存测名记录失败: $e');
    }
  }

  /// 清空起名结果
  void clearNamingResults() {
    _namingResults = [];
    _clearNamingError();
    notifyListeners();
  }

  /// 清空测名结果
  void clearAnalysisResult() {
    _currentAnalysisName = '';
    _nameAnalysis = '';
    _clearAnalysisError();
    notifyListeners();
  }

  // 私有方法
  void _setGeneratingNames(bool value) {
    _isGeneratingNames = value;
    notifyListeners();
  }

  void _setNamingError(String error) {
    _namingError = error;
    notifyListeners();
  }

  void _clearNamingError() {
    _namingError = null;
  }

  void _setAnalyzingName(bool value) {
    _isAnalyzingName = value;
    notifyListeners();
  }

  void _setAnalysisError(String error) {
    _analysisError = error;
    notifyListeners();
  }

  void _clearAnalysisError() {
    _analysisError = null;
  }
}
