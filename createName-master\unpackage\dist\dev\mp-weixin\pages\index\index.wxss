@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.radio-group {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  margin-bottom: 10px;
}
.radio-group .radio {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
          flex-wrap: nowrap;
  width: 50%;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  margin-bottom: 5px;
}
.form-item {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
          flex-wrap: nowrap;
  height: 10%;
  margin: 10px, 0;
  background-color: #c8c7cc;
  -webkit-border-radius: 10px;
          border-radius: 10px;
}
.form-item .img {
  width: 20px;
  height: 20px;
  padding: 2px;
}
.form-item .button {
  background-color: #000000;
  color: #ffffff;
}
.name-list {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.name-list .name-container {
  margin: 10px 20px;
  padding: 5px 5px;
  background-color: #f1f1f1;
  border: #333333 solid 1px;
}
.name-list .name-container .name-info {
  font-size: 26px;
  font-weight: bold;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
          flex-wrap: nowrap;
}
.name-list .name-container .sentence {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 30px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
          flex-wrap: nowrap;
}
.name-list .name-container .sentence .active {
  color: #007AFF;
}
.name-list .name-container .sentence .black {
  color: #333333;
}
.name-list .name-container .name-other {
  pading: 2px 2px;
}

