import '../lib/models/naming_model.dart';
import '../lib/services/naming_service.dart';
import '../lib/data/poetry_data.dart';

/// 起名功能演示
void main() async {
  print('=== 起名测名功能演示 ===\n');
  
  final namingService = NamingService();
  
  // 演示1: 验证功能
  print('1. 姓氏格式验证演示:');
  print('   "王" -> ${namingService.isValidSurname('王')}');
  print('   "李雅" -> ${namingService.isValidSurname('李雅')}');
  print('   "abc" -> ${namingService.isValidSurname('abc')}');
  print('   "" -> ${namingService.isValidSurname('')}');
  print('');
  
  print('2. 姓名格式验证演示:');
  print('   "王小明" -> ${namingService.isValidFullName('王小明')}');
  print('   "李雅静" -> ${namingService.isValidFullName('李雅静')}');
  print('   "王" -> ${namingService.isValidFullName('王')}');
  print('   "王小明小" -> ${namingService.isValidFullName('王小明小')}');
  print('');
  
  // 演示2: 诗词数据
  print('3. 诗词数据统计:');
  print('   唐诗: ${PoetryData.tangshi.length} 首');
  print('   宋词: ${PoetryData.songci.length} 首');
  print('   诗经: ${PoetryData.shijing.length} 首');
  print('   楚辞: ${PoetryData.chuci.length} 首');
  print('   乐府: ${PoetryData.yuefu.length} 首');
  print('   古诗: ${PoetryData.gushi.length} 首');
  print('   总计: ${PoetryData.tangshi.length + PoetryData.songci.length + PoetryData.shijing.length + PoetryData.chuci.length + PoetryData.yuefu.length + PoetryData.gushi.length} 首');
  print('');
  
  // 演示3: 起名功能
  print('4. 起名功能演示:');
  
  for (final poetryType in PoetryType.values) {
    print('\n   === ${poetryType.displayName}起名 ===');
    try {
      final results = await namingService.generateNames(
        surname: '王',
        poetryType: poetryType,
        count: 2,
      );
      
      for (int i = 0; i < results.length; i++) {
        final result = results[i];
        print('   ${i + 1}. ${result.fullName}');
        print('      出处: ${result.title} - ${result.author} (${result.dynasty})');
        print('      诗句: ${result.fullSentence}');
        print('      典籍: ${result.book}');
        
        // 显示字符高亮效果
        print('      高亮: [${result.sentence.map((char) {
          return result.isCharInName(char) ? '【$char】' : char;
        }).join('')}]');
        print('');
      }
    } catch (e) {
      print('   生成失败: $e');
    }
  }
  
  // 演示4: 诗词类型枚举
  print('5. 诗词类型枚举演示:');
  for (final type in PoetryType.values) {
    print('   ${type.name} -> ${type.displayName}');
  }
  print('');
  
  // 演示5: 示例诗词内容
  print('6. 示例诗词内容:');
  print('   唐诗示例:');
  final tangshiSample = PoetryData.tangshi.first;
  print('     标题: ${tangshiSample.title}');
  print('     作者: ${tangshiSample.author}');
  print('     内容: ${tangshiSample.content}');
  print('     朝代: ${tangshiSample.dynasty}');
  print('     出处: ${tangshiSample.book}');
  print('');
  
  print('   宋词示例:');
  final songciSample = PoetryData.songci.first;
  print('     标题: ${songciSample.title}');
  print('     作者: ${songciSample.author}');
  print('     内容: ${songciSample.content}');
  print('     朝代: ${songciSample.dynasty}');
  print('     出处: ${songciSample.book}');
  print('');
  
  print('   诗经示例:');
  final shijingSample = PoetryData.shijing.first;
  print('     标题: ${shijingSample.title}');
  print('     作者: ${shijingSample.author ?? '佚名'}');
  print('     内容: ${shijingSample.content}');
  print('     朝代: ${shijingSample.dynasty}');
  print('     出处: ${shijingSample.book}');
  print('');
  
  // 演示6: 起名结果详细信息
  print('7. 起名结果详细分析:');
  try {
    final detailResults = await namingService.generateNames(
      surname: '李',
      poetryType: PoetryType.tangshi,
      count: 1,
    );
    
    if (detailResults.isNotEmpty) {
      final result = detailResults.first;
      print('   完整姓名: ${result.fullName}');
      print('   姓氏: ${result.surname}');
      print('   名字字符: ${result.name.join(', ')}');
      print('   诗句字符: ${result.sentence.join('')}');
      print('   诗词类型: ${result.poetryType.displayName}');
      print('   创建时间: ${result.createdAt}');
      print('   字符匹配检查:');
      for (final char in result.name) {
        print('     "$char" 在诗句中: ${result.isCharInName(char)}');
      }
    }
  } catch (e) {
    print('   详细分析失败: $e');
  }
  
  print('\n=== 演示完成 ===');
  print('');
  print('功能特点总结:');
  print('✓ 支持6种诗词类型起名');
  print('✓ 智能字符提取和验证');
  print('✓ 完整的出处信息展示');
  print('✓ 诗句中字符高亮显示');
  print('✓ 丰富的诗词数据库');
  print('✓ 严格的格式验证');
  print('✓ 面向对象的设计架构');
  print('');
  print('注意: 本演示仅展示核心逻辑，实际应用中还包含:');
  print('- AI测名分析功能');
  print('- 流式输出界面');
  print('- 历史记录保存');
  print('- 用户友好的界面');
  print('- 错误处理和用户反馈');
}

/// 辅助函数：格式化显示诗词信息
void displayPoetry(Poetry poetry) {
  print('   《${poetry.title}》');
  print('   作者: ${poetry.author ?? '佚名'}');
  print('   朝代: ${poetry.dynasty}');
  print('   出处: ${poetry.book}');
  print('   内容: ${poetry.content}');
}

/// 辅助函数：格式化显示起名结果
void displayNamingResult(NamingResult result, int index) {
  print('   ${index}. 【${result.fullName}】');
  print('      出处: ${result.title} - ${result.author}');
  print('      诗句: ${result.fullSentence}');
  print('      类型: ${result.poetryType.displayName}');
  
  // 显示字符在诗句中的位置
  final highlightedSentence = result.sentence.map((char) {
    return result.isCharInName(char) ? '【$char】' : char;
  }).join('');
  print('      高亮: $highlightedSentence');
}
