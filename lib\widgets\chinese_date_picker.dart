import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class ChineseDatePicker {
  /// 显示中文日期选择器
  static Future<DateTime?> showChineseDatePicker({
    required BuildContext context,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    final now = DateTime.now();
    final initial = initialDate ?? now;
    final first = firstDate ?? DateTime(1900);
    final last = lastDate ?? DateTime(2100);

    return await showModalBottomSheet<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return ChineseDatePickerWidget(
          initialDate: initial,
          firstDate: first,
          lastDate: last,
        );
      },
    );
  }
}

class ChineseDatePickerWidget extends StatefulWidget {
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;

  const ChineseDatePickerWidget({
    Key? key,
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
  }) : super(key: key);

  @override
  State<ChineseDatePickerWidget> createState() => _ChineseDatePickerWidgetState();
}

class _ChineseDatePickerWidgetState extends State<ChineseDatePickerWidget> {
  late int selectedYear;
  late int selectedMonth;
  late int selectedDay;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year;
    selectedMonth = widget.initialDate.month;
    selectedDay = widget.initialDate.day;
  }

  List<int> get years {
    return List.generate(
      widget.lastDate.year - widget.firstDate.year + 1,
      (index) => widget.firstDate.year + index,
    );
  }

  List<int> get months => List.generate(12, (index) => index + 1);

  List<int> get days {
    final daysInMonth = DateTime(selectedYear, selectedMonth + 1, 0).day;
    return List.generate(daysInMonth, (index) => index + 1);
  }

  String getChineseMonth(int month) {
    const monthNames = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    return monthNames[month - 1];
  }

  String getChineseDay(int day) {
    const numbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const tens = ['', '十', '二十', '三十'];
    
    if (day < 10) {
      return '${numbers[day]}日';
    } else if (day == 10) {
      return '十日';
    } else if (day < 20) {
      return '十${numbers[day - 10]}日';
    } else {
      final ten = day ~/ 10;
      final unit = day % 10;
      return '${tens[ten]}${unit == 0 ? '' : numbers[unit]}日';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 标题栏
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              const Text(
                '选择日期',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  final selectedDate = DateTime(selectedYear, selectedMonth, selectedDay);
                  Navigator.of(context).pop(selectedDate);
                },
                child: const Text('确定'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 选择器
          Expanded(
            child: Row(
              children: [
                // 年份选择器
                Expanded(
                  child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectedYear = years[index];
                        // 检查选中的日期是否有效
                        final maxDay = DateTime(selectedYear, selectedMonth + 1, 0).day;
                        if (selectedDay > maxDay) {
                          selectedDay = maxDay;
                        }
                      });
                    },
                    children: years.map((year) => Center(
                      child: Text('${year}年'),
                    )).toList(),
                    scrollController: FixedExtentScrollController(
                      initialItem: years.indexOf(selectedYear),
                    ),
                  ),
                ),
                // 月份选择器
                Expanded(
                  child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectedMonth = months[index];
                        // 检查选中的日期是否有效
                        final maxDay = DateTime(selectedYear, selectedMonth + 1, 0).day;
                        if (selectedDay > maxDay) {
                          selectedDay = maxDay;
                        }
                      });
                    },
                    children: months.map((month) => Center(
                      child: Text(getChineseMonth(month)),
                    )).toList(),
                    scrollController: FixedExtentScrollController(
                      initialItem: months.indexOf(selectedMonth),
                    ),
                  ),
                ),
                // 日期选择器
                Expanded(
                  child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectedDay = days[index];
                      });
                    },
                    children: days.map((day) => Center(
                      child: Text(getChineseDay(day)),
                    )).toList(),
                    scrollController: FixedExtentScrollController(
                      initialItem: days.indexOf(selectedDay),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 中文时间选择器
class ChineseTimePicker {
  static Future<TimeOfDay?> showChineseTimePicker({
    required BuildContext context,
    TimeOfDay? initialTime,
  }) async {
    final initial = initialTime ?? TimeOfDay.now();

    return await showModalBottomSheet<TimeOfDay>(
      context: context,
      builder: (BuildContext context) {
        return ChineseTimePickerWidget(initialTime: initial);
      },
    );
  }
}

class ChineseTimePickerWidget extends StatefulWidget {
  final TimeOfDay initialTime;

  const ChineseTimePickerWidget({
    Key? key,
    required this.initialTime,
  }) : super(key: key);

  @override
  State<ChineseTimePickerWidget> createState() => _ChineseTimePickerWidgetState();
}

class _ChineseTimePickerWidgetState extends State<ChineseTimePickerWidget> {
  late int selectedHour;
  late int selectedMinute;

  @override
  void initState() {
    super.initState();
    selectedHour = widget.initialTime.hour;
    selectedMinute = widget.initialTime.minute;
  }

  List<int> get hours => List.generate(24, (index) => index);
  List<int> get minutes => List.generate(60, (index) => index);

  String getChineseHour(int hour) {
    const numbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const tens = ['', '十', '二十'];
    
    if (hour < 10) {
      return '${numbers[hour]}时';
    } else if (hour < 20) {
      return '十${hour == 10 ? '' : numbers[hour - 10]}时';
    } else {
      final ten = hour ~/ 10;
      final unit = hour % 10;
      return '${tens[ten]}${unit == 0 ? '' : numbers[unit]}时';
    }
  }

  String getChineseMinute(int minute) {
    const numbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const tens = ['', '十', '二十', '三十', '四十', '五十'];
    
    if (minute == 0) {
      return '整';
    } else if (minute < 10) {
      return '${numbers[minute]}分';
    } else {
      final ten = minute ~/ 10;
      final unit = minute % 10;
      return '${tens[ten]}${unit == 0 ? '' : numbers[unit]}分';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 标题栏
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              const Text(
                '选择时间',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  final selectedTime = TimeOfDay(hour: selectedHour, minute: selectedMinute);
                  Navigator.of(context).pop(selectedTime);
                },
                child: const Text('确定'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 选择器
          Expanded(
            child: Row(
              children: [
                // 小时选择器
                Expanded(
                  child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectedHour = hours[index];
                      });
                    },
                    children: hours.map((hour) => Center(
                      child: Text(getChineseHour(hour)),
                    )).toList(),
                    scrollController: FixedExtentScrollController(
                      initialItem: selectedHour,
                    ),
                  ),
                ),
                // 分钟选择器
                Expanded(
                  child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectedMinute = minutes[index];
                      });
                    },
                    children: minutes.map((minute) => Center(
                      child: Text(getChineseMinute(minute)),
                    )).toList(),
                    scrollController: FixedExtentScrollController(
                      initialItem: selectedMinute,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
