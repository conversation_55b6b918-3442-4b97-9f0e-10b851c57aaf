# 起名测名功能说明

## 功能概述

本应用新增了基于古典诗词的起名和AI测名功能，让用户可以从传统文化中汲取灵感，为孩子起一个富有诗意和文化内涵的名字。

## 主要特性

### 🎨 诗词起名
- **六大诗词类型**：支持唐诗、宋词、诗经、楚辞、乐府、古诗
- **智能字符提取**：从诗句中随机选择两个字组成名字
- **出处展示**：显示名字来源的完整诗句、作者、朝代等信息
- **视觉高亮**：在诗句中高亮显示选中的字符
- **批量生成**：一次生成6个不同的名字选项

### 🤖 AI测名分析
- **全面分析**：字义、音韵、五行、文化内涵、性格暗示、吉凶评价
- **流式输出**：实时显示AI分析过程，提升用户体验
- **专业解读**：基于传统文化和现代心理学的综合分析
- **温和建议**：避免过于绝对的判断，提供参考性建议

### 💾 历史记录
- **完整保存**：保存起名结果和测名分析的完整内容
- **分类管理**：与其他功能的历史记录统一管理
- **导出功能**：支持导出为文本文件
- **时间戳**：记录每次操作的具体时间

## 技术实现

### 架构设计
```
lib/
├── models/
│   ├── naming_model.dart          # 起名相关数据模型
│   └── naming_model.g.dart        # 自动生成的序列化代码
├── services/
│   └── naming_service.dart        # 起名业务逻辑
├── providers/
│   └── naming_provider.dart       # 状态管理
├── screens/
│   └── naming_screen.dart         # 起名主界面
├── widgets/
│   ├── naming_result_card.dart    # 起名结果卡片
│   └── name_analysis_widget.dart  # 测名分析组件
├── data/
│   └── poetry_data.dart           # 诗词数据
└── test/
    └── naming_test.dart           # 单元测试
```

### 核心组件

#### 1. 数据模型 (naming_model.dart)
- `PoetryType`: 诗词类型枚举
- `Poetry`: 诗词数据模型
- `NamingResult`: 起名结果模型
- `NameAnalysisResult`: 测名结果模型

#### 2. 业务服务 (naming_service.dart)
- 诗词内容清理和处理
- 随机字符选择算法
- 姓氏和姓名格式验证
- 起名结果生成逻辑

#### 3. 状态管理 (naming_provider.dart)
- 起名状态管理（加载、错误、结果）
- 测名状态管理（分析进度、结果）
- AI分析流式输出处理
- 历史记录保存

#### 4. 用户界面
- **Tab布局**：起名和测名功能分离
- **响应式设计**：适配不同屏幕尺寸
- **黑白灰配色**：符合应用整体设计风格
- **流畅动画**：加载状态和过渡效果

## 诗词数据

### 数据来源
- **唐诗**：《唐诗三百首》经典篇目
- **宋词**：著名词人代表作品
- **诗经**：《诗经》中的经典篇章
- **楚辞**：屈原等楚辞名篇
- **乐府**：汉代乐府诗精选
- **古诗**：《古诗十九首》等经典

### 数据特点
- **精选内容**：选择寓意美好、适合起名的诗句
- **完整信息**：包含标题、作者、朝代、出处等
- **文化价值**：体现中华传统文化的深厚底蕴

## AI分析功能

### 分析维度
1. **字义分析**：解释每个字的含义和寓意
2. **音韵分析**：评价名字的读音和音律美感
3. **五行分析**：基于传统五行理论的属性分析
4. **文化内涵**：挖掘名字的文化背景和典故
5. **性格暗示**：分析名字可能暗示的性格特征
6. **吉凶评价**：给出综合的评价和建议

### 技术特点
- **流式处理**：实时显示分析过程
- **专业术语**：使用传统文化和现代心理学术语
- **温和表达**：避免过于绝对的判断
- **个性化**：针对具体姓名进行定制化分析

## 使用指南

### 起名流程
1. 输入姓氏（1-2个中文字符）
2. 选择诗词类型（唐诗、宋词等）
3. 点击"开始起名"按钮
4. 查看生成的名字选项
5. 点击保存按钮将喜欢的名字保存到历史

### 测名流程
1. 输入完整姓名（2-4个中文字符）
2. 点击"开始分析"按钮
3. 观看AI实时分析过程
4. 查看完整的分析报告
5. 分析结果自动保存到历史记录

## 注意事项

### 免责声明
- 本功能仅供娱乐和文化欣赏使用
- 起名建议不应作为最终决策的唯一依据
- AI分析结果仅供参考，不具有科学预测价值
- 建议结合个人喜好和家庭文化背景综合考虑

### 使用限制
- 仅支持中文姓名
- 姓氏限制为1-2个字符
- 完整姓名限制为2-4个字符
- 需要网络连接进行AI分析

## 未来规划

### 功能扩展
- [ ] 增加更多诗词数据源
- [ ] 支持自定义诗词导入
- [ ] 添加名字评分系统
- [ ] 支持批量测名功能
- [ ] 增加名字寓意详解

### 技术优化
- [ ] 优化AI分析算法
- [ ] 提升起名算法智能度
- [ ] 增加离线模式支持
- [ ] 优化界面交互体验

## 技术支持

如有问题或建议，请通过以下方式联系：
- 应用内反馈功能
- 项目GitHub Issues
- 开发者邮箱

---

*本功能基于中华传统文化开发，旨在传承和弘扬优秀的文化遗产。*
