/// Markdown格式清理工具类
/// 清理Markdown格式符号，但保留emoji表情符号
class MarkdownCleaner {
  /// 清理Markdown格式符号，保留emoji和正常标点
  static String clean(String text) {
    if (text.isEmpty) return text;

    // 移除常见的Markdown格式符号
    String cleaned = text;

    // 移除代码块符号 (```) - 先处理代码块，避免影响其他处理
    cleaned = cleaned.replaceAll(RegExp(r'```[\s\S]*?```'), '');

    // 移除行内代码符号 (`)
    cleaned = cleaned.replaceAll(RegExp(r'`([^`]*)`'), r'$1');

    // 移除标题符号 (#) - 但保留在emoji中的#
    cleaned = cleaned.replaceAll(RegExp(r'^#{1,6}\s+', multiLine: true), '');
    cleaned = cleaned.replaceAll(RegExp(r'\s#{1,6}\s+'), ' ');

    // 移除粗体符号 (**) - 更精确的匹配
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'\*\*([^*\n]+?)\*\*'),
      (match) => match.group(1) ?? ''
    );

    // 移除斜体符号 (*) - 避免与emoji冲突，只处理明确的斜体格式
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'(?<!\*)\*([^*\n]+?)\*(?!\*)'),
      (match) => match.group(1) ?? ''
    );

    // 移除下划线格式 (_) - 只处理明确的格式，避免影响正常下划线
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'\b_([^_\s]+?)_\b'),
      (match) => match.group(1) ?? ''
    );

    // 移除删除线 (~) - 只处理明确的删除线格式
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'~~([^~\n]+?)~~'),
      (match) => match.group(1) ?? ''
    );

    // 移除链接格式 [text](url)
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'\[([^\]]+)\]\([^)]+\)'),
      (match) => match.group(1) ?? ''
    );

    // 移除列表符号 (- 或 *) - 只在行首
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*]\s+', multiLine: true), '');

    // 移除数字列表符号 (1. 2. 等) - 只在行首
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 移除引用符号 (>) - 只在行首
    cleaned = cleaned.replaceAll(RegExp(r'^\s*>\s+', multiLine: true), '');

    // 移除多余的空行，但保留段落分隔
    cleaned = cleaned.replaceAll(RegExp(r'\n\s*\n\s*\n+'), '\n\n');

    return cleaned.trim();
  }

  /// 检查文本是否包含emoji
  static bool containsEmoji(String text) {
    // 简单的emoji检测正则表达式
    final emojiRegex = RegExp(r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]', unicode: true);
    return emojiRegex.hasMatch(text);
  }

  /// 保留emoji的温和清理
  static String gentleClean(String text) {
    if (text.isEmpty) return text;

    // 如果包含emoji，使用更温和的清理方式
    if (containsEmoji(text)) {
      String cleaned = text;

      // 只移除明确的Markdown格式，避免影响emoji
      cleaned = cleaned.replaceAllMapped(
        RegExp(r'\*\*([^*\n]+?)\*\*'),
        (match) => match.group(1) ?? ''
      ); // 粗体

      cleaned = cleaned.replaceAllMapped(
        RegExp(r'(?<!\*)\*([^*\n]+?)\*(?!\*)'),
        (match) => match.group(1) ?? ''
      ); // 斜体

      cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*]\s+', multiLine: true), ''); // 列表
      cleaned = cleaned.replaceAll(RegExp(r'^\s*#{1,6}\s+', multiLine: true), ''); // 标题
      cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), ''); // 数字列表

      return cleaned.trim();
    }

    // 如果不包含emoji，使用标准清理
    return clean(text);
  }
}
