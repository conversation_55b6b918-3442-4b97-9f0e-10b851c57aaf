/// Markdown格式清理工具类
class MarkdownCleaner {
  /// 清理Markdown格式符号
  static String clean(String text) {
    if (text.isEmpty) return text;

    // 移除常见的Markdown格式符号
    String cleaned = text;

    // 移除代码块符号 (```) - 先处理代码块，避免影响其他处理
    cleaned = cleaned.replaceAll(RegExp(r'```[\s\S]*?```'), '');

    // 移除行内代码符号 (`)
    cleaned = cleaned.replaceAll(RegExp(r'`([^`]*)`'), r'$1');

    // 移除标题符号 (#)
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), '');

    // 移除粗体符号 (**) - 使用非贪婪匹配
    cleaned = cleaned.replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1');

    // 移除斜体符号 (*) - 确保不匹配粗体
    cleaned = cleaned.replaceAll(RegExp(r'(?<!\*)\*([^*]+?)\*(?!\*)'), r'$1');

    // 移除下划线格式 (_)
    cleaned = cleaned.replaceAll(RegExp(r'_([^_]+?)_'), r'$1');

    // 移除删除线 (~)
    cleaned = cleaned.replaceAll(RegExp(r'~([^~]+?)~'), r'$1');

    // 移除链接格式 [text](url)
    cleaned = cleaned.replaceAll(RegExp(r'\[([^\]]+)\]\([^)]+\)'), r'$1');

    // 移除列表符号 (- 或 *)
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*]\s+', multiLine: true), '');

    // 移除数字列表符号 (1. 2. 等)
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 移除引用符号 (>)
    cleaned = cleaned.replaceAll(RegExp(r'^\s*>\s+', multiLine: true), '');

    // 移除剩余的单独符号 - 但要小心不要移除文本中的正常符号
    cleaned = cleaned.replaceAll(RegExp(r'[_~`*#]'), '');

    return cleaned.trim();
  }
}
