import 'package:flutter_test/flutter_test.dart';
import 'package:daotianji/services/naming_service.dart';
import 'package:daotianji/models/naming_model.dart';

void main() {
  group('NamingService Tests', () {
    late NamingService namingService;

    setUp(() {
      namingService = NamingService();
    });

    test('should validate surname correctly', () {
      // 有效姓氏
      expect(namingService.isValidSurname('王'), true);
      expect(namingService.isValidSurname('李'), true);
      expect(namingService.isValidSurname('张'), true);
      expect(namingService.isValidSurname('欧阳'), true);

      // 无效姓氏
      expect(namingService.isValidSurname(''), false);
      expect(namingService.isValidSurname('abc'), false);
      expect(namingService.isValidSurname('王李张'), false);
      expect(namingService.isValidSurname('123'), false);
    });

    test('should validate full name correctly', () {
      // 有效姓名
      expect(namingService.isValidFullName('王明'), true);
      expect(namingService.isValidFullName('李小明'), true);
      expect(namingService.isValidFullName('张雅静'), true);
      expect(namingService.isValidFullName('欧阳修'), true);

      // 无效姓名
      expect(namingService.isValidFullName(''), false);
      expect(namingService.isValidFullName('王'), false);
      expect(namingService.isValidFullName('王李张明华'), false);
      expect(namingService.isValidFullName('abc'), false);
      expect(namingService.isValidFullName('123'), false);
    });

    test('should generate names from poetry', () async {
      final results = await namingService.generateNames(
        surname: '王',
        poetryType: PoetryType.tangshi,
        count: 3,
      );

      expect(results, isNotEmpty);
      expect(results.length, lessThanOrEqualTo(3));

      for (final result in results) {
        // 检查基本属性
        expect(result.surname, '王');
        expect(result.givenName.length, 2);
        expect(result.fullName, '王${result.givenName}');
        expect(result.selectedChars.length, 2);
        expect(result.poetry, isNotNull);
        expect(result.createdAt, isNotNull);

        // 检查选中的字符确实在名字中
        for (final char in result.selectedChars) {
          expect(result.givenName.contains(char), true);
        }

        // 检查诗词信息
        expect(result.poetry.content, isNotEmpty);
        expect(result.poetry.title, isNotEmpty);
        expect(result.poetry.book, isNotEmpty);
        expect(result.poetry.dynasty, isNotEmpty);
      }
    });

    test('should generate different names for different poetry types', () async {
      final tangshiResults = await namingService.generateNames(
        surname: '李',
        poetryType: PoetryType.tangshi,
        count: 2,
      );

      final songciResults = await namingService.generateNames(
        surname: '李',
        poetryType: PoetryType.songci,
        count: 2,
      );

      expect(tangshiResults, isNotEmpty);
      expect(songciResults, isNotEmpty);

      // 检查诗词类型不同
      final tangshiBooks = tangshiResults.map((r) => r.poetry.book).toSet();
      final songciBooks = songciResults.map((r) => r.poetry.book).toSet();
      
      // 唐诗和宋词的出处应该不同
      expect(tangshiBooks.intersection(songciBooks).isEmpty, true);
    });

    test('should handle empty poetry content gracefully', () async {
      // 测试边界情况
      final results = await namingService.generateNames(
        surname: '张',
        poetryType: PoetryType.chuci,
        count: 1,
      );

      // 即使某些诗词处理失败，也应该能返回一些结果
      expect(results, isA<List<NamingResult>>());
    });

    test('should generate names with correct character selection logic', () async {
      final results = await namingService.generateNames(
        surname: '陈',
        poetryType: PoetryType.shijing,
        count: 1,
      );

      if (results.isNotEmpty) {
        final result = results.first;
        
        // 检查名字字符是否按顺序选择
        final givenNameChars = result.givenName.split('');
        expect(givenNameChars, equals(result.selectedChars));
        
        // 检查字符是否来自诗词内容
        final cleanContent = result.poetry.content.replaceAll(RegExp(r'[<>《》！*\(\^\)\$%~!@#…&%￥—\+=、。，？；''""：·`]'), '');
        for (final char in result.selectedChars) {
          expect(cleanContent.contains(char), true);
        }
      }
    });
  });
}
