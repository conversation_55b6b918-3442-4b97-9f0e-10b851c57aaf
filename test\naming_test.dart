import 'package:flutter_test/flutter_test.dart';
import 'package:liuyaozhanbu/models/naming_model.dart';
import 'package:liuyaozhanbu/services/naming_service.dart';
import 'package:liuyaozhanbu/data/poetry_data.dart';

void main() {
  group('起名功能测试', () {
    late NamingService namingService;

    setUp(() {
      namingService = NamingService();
    });

    test('验证姓氏格式', () {
      expect(namingService.isValidSurname('王'), true);
      expect(namingService.isValidSurname('李'), true);
      expect(namingService.isValidSurname('张三'), true);
      expect(namingService.isValidSurname(''), false);
      expect(namingService.isValidSurname('王三四'), false);
      expect(namingService.isValidSurname('abc'), false);
      expect(namingService.isValidSurname('123'), false);
    });

    test('验证姓名格式', () {
      expect(namingService.isValidFullName('王小明'), true);
      expect(namingService.isValidFullName('李雅静'), true);
      expect(namingService.isValidFullName('张三'), true);
      expect(namingService.isValidFullName('欧阳修'), true);
      expect(namingService.isValidFullName(''), false);
      expect(namingService.isValidFullName('王'), false);
      expect(namingService.isValidFullName('王小明小'), false);
      expect(namingService.isValidFullName('abc'), false);
    });

    test('生成名字 - 唐诗', () async {
      final results = await namingService.generateNames(
        surname: '王',
        poetryType: PoetryType.tangshi,
        count: 3,
      );

      expect(results.length, greaterThan(0));
      expect(results.length, lessThanOrEqualTo(3));
      
      for (final result in results) {
        expect(result.surname, '王');
        expect(result.name.length, 2);
        expect(result.poetryType, PoetryType.tangshi);
        expect(result.dynasty, '唐代');
        expect(result.fullName.length, 3); // 王 + 2个字
      }
    });

    test('生成名字 - 宋词', () async {
      final results = await namingService.generateNames(
        surname: '李',
        poetryType: PoetryType.songci,
        count: 2,
      );

      expect(results.length, greaterThan(0));
      expect(results.length, lessThanOrEqualTo(2));
      
      for (final result in results) {
        expect(result.surname, '李');
        expect(result.name.length, 2);
        expect(result.poetryType, PoetryType.songci);
        expect(result.dynasty, '宋代');
      }
    });

    test('生成名字 - 诗经', () async {
      final results = await namingService.generateNames(
        surname: '张',
        poetryType: PoetryType.shijing,
        count: 2,
      );

      expect(results.length, greaterThan(0));
      
      for (final result in results) {
        expect(result.surname, '张');
        expect(result.poetryType, PoetryType.shijing);
        expect(result.dynasty, '先秦');
      }
    });

    test('生成名字 - 楚辞', () async {
      final results = await namingService.generateNames(
        surname: '陈',
        poetryType: PoetryType.chuci,
        count: 2,
      );

      expect(results.length, greaterThan(0));
      
      for (final result in results) {
        expect(result.surname, '陈');
        expect(result.poetryType, PoetryType.chuci);
        expect(result.dynasty, '先秦');
      }
    });

    test('检查名字字符是否在诗句中', () async {
      final results = await namingService.generateNames(
        surname: '王',
        poetryType: PoetryType.tangshi,
        count: 1,
      );

      expect(results.length, greaterThan(0));
      
      final result = results.first;
      for (final nameChar in result.name) {
        expect(result.isCharInName(nameChar), true);
        expect(result.sentence.contains(nameChar), true);
      }
    });
  });

  group('诗词数据测试', () {
    test('唐诗数据完整性', () {
      expect(PoetryData.tangshi.length, greaterThan(10));
      
      for (final poetry in PoetryData.tangshi) {
        expect(poetry.content.isNotEmpty, true);
        expect(poetry.title.isNotEmpty, true);
        expect(poetry.book, '唐诗三百首');
        expect(poetry.dynasty, '唐代');
      }
    });

    test('宋词数据完整性', () {
      expect(PoetryData.songci.length, greaterThan(5));
      
      for (final poetry in PoetryData.songci) {
        expect(poetry.content.isNotEmpty, true);
        expect(poetry.title.isNotEmpty, true);
        expect(poetry.book, '宋词三百首');
        expect(poetry.dynasty, '宋代');
      }
    });

    test('诗经数据完整性', () {
      expect(PoetryData.shijing.length, greaterThan(3));
      
      for (final poetry in PoetryData.shijing) {
        expect(poetry.content.isNotEmpty, true);
        expect(poetry.title.isNotEmpty, true);
        expect(poetry.book, '诗经');
        expect(poetry.dynasty, '先秦');
      }
    });

    test('楚辞数据完整性', () {
      expect(PoetryData.chuci.length, greaterThan(2));
      
      for (final poetry in PoetryData.chuci) {
        expect(poetry.content.isNotEmpty, true);
        expect(poetry.title.isNotEmpty, true);
        expect(poetry.book, '楚辞');
        expect(poetry.dynasty, '先秦');
      }
    });
  });

  group('诗词类型枚举测试', () {
    test('诗词类型显示名称', () {
      expect(PoetryType.tangshi.displayName, '唐诗');
      expect(PoetryType.songci.displayName, '宋词');
      expect(PoetryType.shijing.displayName, '诗经');
      expect(PoetryType.chuci.displayName, '楚辞');
      expect(PoetryType.yuefu.displayName, '乐府');
      expect(PoetryType.gushi.displayName, '古诗');
    });

    test('所有诗词类型都有对应数据', () {
      for (final type in PoetryType.values) {
        List<Poetry> data;
        switch (type) {
          case PoetryType.tangshi:
            data = PoetryData.tangshi;
            break;
          case PoetryType.songci:
            data = PoetryData.songci;
            break;
          case PoetryType.shijing:
            data = PoetryData.shijing;
            break;
          case PoetryType.chuci:
            data = PoetryData.chuci;
            break;
          case PoetryType.yuefu:
            data = PoetryData.yuefu;
            break;
          case PoetryType.gushi:
            data = PoetryData.gushi;
            break;
        }
        expect(data.isNotEmpty, true, reason: '${type.displayName}数据不能为空');
      }
    });
  });
}
