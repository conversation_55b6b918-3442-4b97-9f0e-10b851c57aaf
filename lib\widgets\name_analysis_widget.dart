import 'package:flutter/material.dart';

class NameAnalysisWidget extends StatelessWidget {
  final String name;
  final String analysis;
  final bool isLoading;

  const NameAnalysisWidget({
    super.key,
    required this.name,
    required this.analysis,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分析标题
        _buildAnalysisHeader(),
        
        const SizedBox(height: 16),
        
        // 分析内容
        _buildAnalysisContent(),
      ],
    );
  }

  Widget _buildAnalysisHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '姓名分析',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Text(
                '分析姓名：',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFFE5E5E5),
                ),
              ),
              Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisContent() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE5E5E5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isLoading)
            _buildLoadingIndicator()
          else if (analysis.isEmpty)
            _buildEmptyState()
          else
            _buildAnalysisText(),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Column(
      children: [
        SizedBox(height: 20),
        CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1A1A1A)),
        ),
        SizedBox(height: 16),
        Text(
          'AI正在分析中，请稍候...',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
        ),
        SizedBox(height: 20),
      ],
    );
  }

  Widget _buildEmptyState() {
    return const Column(
      children: [
        SizedBox(height: 20),
        Icon(
          Icons.psychology_outlined,
          size: 48,
          color: Color(0xFFE5E5E5),
        ),
        SizedBox(height: 16),
        Text(
          '点击"开始分析"按钮获取AI分析结果',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
        ),
        SizedBox(height: 20),
      ],
    );
  }

  Widget _buildAnalysisText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分析内容
        SelectableText(
          analysis,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF1A1A1A),
            height: 1.6,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 免责声明
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFE5E5E5)),
          ),
          child: const Text(
            '⚠️ 免责声明：本分析结果仅供娱乐参考，不应作为人生决策的依据。',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF666666),
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }
}
