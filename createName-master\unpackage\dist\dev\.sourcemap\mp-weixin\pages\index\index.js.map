{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///F:/video/2021name/newName/pages/index/index.vue?2b7e", "webpack:///F:/video/2021name/newName/pages/index/index.vue?60e5", "webpack:///F:/video/2021name/newName/pages/index/index.vue?56bf", "uni-app:///pages/index/index.vue", "webpack:///F:/video/2021name/newName/pages/index/index.vue?add3", "webpack:///F:/video/2021name/newName/pages/index/index.vue?649f", "webpack:///F:/video/2021name/newName/pages/index/index.vue?6bca", "webpack:///F:/video/2021name/newName/pages/index/index.vue?da78"], "names": ["createPage", "Page"], "mappings": ";;;;;;;;;;;;;;;;;;;;kDAAA;AACA;AACA,4F;AACAA,UAAU,CAACC,cAAD,CAAV,C;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAod;AACpd;AACyD;AACL;AACc;;;AAGlE;AACoM;AACpM,gBAAgB,6MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,kbAAM;AACR,EAAE,2bAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sbAAU;AACZ;AACA;;AAEA;AAC+F;AAC/F,WAAW,iHAAM,iBAAiB,yHAAM;;AAExC;AACe,gF;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmD9wB;AACA,MADA,kBACA;AACA;AACA,iDADA;AAEA,gBAFA;AAGA,kBAHA;AAIA,kBAJA;;AAMA,GARA;AASA,QATA,oBASA,EATA;AAUA;AACA,eADA,uBACA,GADA,EACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KARA;AASA;AACA,eAVA,uBAUA,GAVA,EAUA,GAVA,EAUA;AACA;AACA;AACA,KAbA;AAcA;AACA,oBAfA,4BAeA,GAfA,EAeA;AACA;AACA;AACA,KAlBA;;AAoBA;AACA,mBArBA,2BAqBA,GArBA,EAqBA,GArBA,EAqBA,OArBA,EAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAlCA;AAmCA,iBAnCA,yBAmCA,GAnCA,EAmCA,GAnCA,EAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAtDA;AAuDA,WAvDA,qBAuDA;AACA,mBADA,GACA,2BADA;AAEA,mBAFA,GAEA,sBAFA;AAGA,qBAHA,GAGA,0BAHA;AAIA,+BAJA;AAKA;AACA,iCADA,IALA;;AAQA,8BARA;AASA;AACA,mCADA,IATA;;AAYA,wCAZA;AAaA;AACA,kCADA,IAbA;;;AAiBA;AACA,8BADA,CACA;AACA,uCADA;AAEA;AACA,kCADA,EAFA,EADA;;;AAOA,sBAPA,CAOA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBArBA;AAsBA;AACA,mBAlCA,CAjBA;;AAqDA,KA5GA,EAVA,E;;;;;;;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAw3C,CAAgB,y1CAAG,EAAC,C;;;;;;;;;;ACA54C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAsiB,CAAgB,klBAAG,EAAC,C;;;;;;;;;;;ACA1jB;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&filter-modules=eyJ0b29scyI6eyJ0eXBlIjoic2NyaXB0IiwiY29udGVudCI6ImZ1bmN0aW9uIGZuKGFyciwgdmFsdWUpIHtcblx0aWYoYXJyLmluZGV4T2YodmFsdWUpIDwgMCkge1xuXHRcdHJldHVybiBmYWxzZTtcblx0fSBlbHNlIHtcblx0XHRyZXR1cm4gdHJ1ZTtcblx0fVxufVxubW9kdWxlLmV4cG9ydHMgPSB7XG5cdGZuOmZuXG59OyIsInN0YXJ0IjoxMzM1LCJhdHRycyI6eyJtb2R1bGUiOiJ0b29scyIsImxhbmciOiJ3eHMifSwiZW5kIjoxNDc3fX0%3D&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./index.vue?vue&type=custom&index=0&blockType=script&module=tools&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--16-0!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&filter-modules=eyJ0b29scyI6eyJ0eXBlIjoic2NyaXB0IiwiY29udGVudCI6ImZ1bmN0aW9uIGZuKGFyciwgdmFsdWUpIHtcblx0aWYoYXJyLmluZGV4T2YodmFsdWUpIDwgMCkge1xuXHRcdHJldHVybiBmYWxzZTtcblx0fSBlbHNlIHtcblx0XHRyZXR1cm4gdHJ1ZTtcblx0fVxufVxubW9kdWxlLmV4cG9ydHMgPSB7XG5cdGZuOmZuXG59OyIsInN0YXJ0IjoxMzM1LCJhdHRycyI6eyJtb2R1bGUiOiJ0b29scyIsImxhbmciOiJ3eHMifSwiZW5kIjoxNDc3fX0%3D&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--12-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--12-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view>\r\n\t\t\t<radio-group class=\"radio-group\" @change=\"radioChange\">\r\n\t\t\t\t<label class=\"radio\" v-for=\"(item, index) in array\" :key=\"index\">\r\n\t\t\t\t\t<view><radio :value=\"item\" color=\"#000000\" :checked=\"index === current\" /></view>\r\n\t\t\t\t\t<view>{{ item }}</view>\r\n\t\t\t\t</label>\r\n\t\t\t</radio-group>\r\n\t\t</view>\r\n\t\t<view class=\"form-item\">\r\n\t\t\t<image class=\"img\" src=\"../../static/icon_search.png\"></image>\r\n\t\t\t<input type=\"text\" v-model=\"userName\" placeholder=\"请输入姓氏\" />\r\n\t\t\t<button class=\"button\" type=\"primary\" size=\"mini\" @click=\"subName\">起名</button>\r\n\t\t</view>\r\n\t\t<view class=\"name-list\">\r\n\t\t\t<view class=\"name-container\" v-for=\"item in nameList\" :key=\"item._id\">\r\n\t\t\t\t<view class=\"name-info\">\r\n\t\t\t\t\t<view>{{ userName }}</view>\r\n\t\t\t\t\t<view v-for=\"(name,index) in item.name\" :key=\"index\">{{name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sentence\">\r\n\t\t\t\t\t<view>[</view>\r\n\t\t\t\t\t<view v-for=\"(sentence,index) in item.sentence\" :key=\"index\"\r\n\t\t\t\t\t:class=\"tools.fn(item.name, sentence)?'active':'black'\">\r\n\t\t\t\t\t\t{{sentence}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>]</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"name-other\">\r\n\t\t\t\t\t<view>{{ item.book }} ● {{ item.title }}</view>\r\n\t\t\t\t\t<view>[{{ item.dynasty }}] {{ item.author }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script module=\"tools\" lang=\"wxs\">\n\tfunction fn(arr, value) {\n\t\tif(arr.indexOf(value) < 0) {\n\t\t\treturn false;\n\t\t} else {\n\t\t\treturn true;\n\t\t}\n\t}\n\tmodule.exports = {\n\t\tfn:fn\n\t};\n</script>\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tarray: ['唐诗', '宋词', '诗经', '楚辞', '乐府', '古诗'],\r\n\t\t\tcurrent: 0,\r\n\t\t\tuserName: '',\r\n\t\t\tnameList: ''\r\n\t\t};\r\n\t},\r\n\tonLoad() {},\r\n\tmethods: {\r\n\t\tradioChange(evt) {\r\n\t\t\tfor (let i = 0; i < this.array.length; i++) {\r\n\t\t\t\tif (this.array[i] === evt.target.value) {\r\n\t\t\t\t\tthis.current = i;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 选取诗文\r\n\t\trandBetween(min, max) {\r\n\t\t\t// [min,max)  max is not included\r\n\t\t\treturn min + Math.floor(Math.random() * (max - min));\r\n\t\t},\r\n\t\t// 清理标点符号\r\n\t\tcleanPunctuation(str) {\r\n\t\t\tconst puncReg = /[<>《》！*\\(\\^\\)\\$%~!@#…&%￥—\\+=、。，？；‘’“”：·`]/g;\r\n\t\t\treturn str.replace(puncReg, '');\r\n\t\t},\r\n\r\n\t\t// 取两个字\r\n\t\trandCharFromStr(str, num, ordered) {\r\n\t\t\tif (typeof ordered === 'undefined') {\r\n\t\t\t\tordered = true;\r\n\t\t\t}\r\n\t\t\tlet randNumArr = this.genRandNumArr(str.length, num);\r\n\t\t\tif (ordered) {\r\n\t\t\t\trandNumArr = randNumArr.sort((a, b) => a - b);\r\n\t\t\t}\r\n\t\t\tlet res = '';\r\n\t\t\tfor (let i = 0; i < randNumArr.length; i++) {\r\n\t\t\t\tres += str.charAt(randNumArr[i]);\r\n\t\t\t}\r\n\t\t\treturn res;\r\n\t\t},\r\n\t\tgenRandNumArr(max, num) {\r\n\t\t\tif (num > max) {\r\n\t\t\t\tnum = max;\r\n\t\t\t\tconsole.log(`max=${max} num = ${num}`);\r\n\t\t\t\t// throw new Error('too large num');\r\n\t\t\t}\r\n\t\t\tconst orderedNum = [];\r\n\t\t\tfor (var i = 0; i < max; i++) {\r\n\t\t\t\torderedNum.push(i);\r\n\t\t\t}\r\n\t\t\tconst res = [];\r\n\t\t\tfor (var i = 0; i < num; i++) {\r\n\t\t\t\tconst randIndex = this.randBetween(0, orderedNum.length);\r\n\t\t\t\tconst randNum = orderedNum[randIndex];\r\n\t\t\t\tres.push(randNum);\r\n\t\t\t\torderedNum.splice(randIndex, 1);\r\n\t\t\t\t// console.log('i=' + i + 'rand=' + rand, orderedNum);\r\n\t\t\t}\r\n\t\t\treturn res;\r\n\t\t},\r\n\t\tasync subName() {\r\n\t\t\tlet arr = Object.keys(this.userName);\r\n\t\t\tlet reg = /^([\\u4E00-\\u9FA5])*$/;\r\n\t\t\tconst shici = this.array[this.current];\r\n\t\t\tif (arr.length == 0) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '姓氏不能为空'\r\n\t\t\t\t});\r\n\t\t\t} else if (arr.length > 2) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '姓氏不能超过两位'\r\n\t\t\t\t});\r\n\t\t\t} else if (!reg.test(this.userName)) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '姓氏必须是汉字'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tawait uniCloud\r\n\t\t\t\t\t.callFunction({\r\n\t\t\t\t\t\tname: 'getName_too',\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tshici\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconst List = res.result.data;\r\n\t\t\t\t\t\tconst Result = [];\r\n\r\n\t\t\t\t\t\tList.forEach((item, index) => {\r\n\t\t\t\t\t\t\tvar object = {};\r\n\t\t\t\t\t\t\tobject = item.content;\r\n\t\t\t\t\t\t\tobject = object.replace(/\\s|<br>|<p>|<\\/p>|　|”|“/g, '');\r\n\t\t\t\t\t\t\tobject = object.replace(/\\(.+\\)/g, '');\r\n\t\t\t\t\t\t\tobject = object.replace(/！|。|？|；/g, object => `${object}|`);\r\n\t\t\t\t\t\t\tobject = object.replace(/\\|$/g, '');\r\n\t\t\t\t\t\t\tlet arr = object.split('|');\r\n\t\t\t\t\t\t\tarr = arr.filter(item => item.length >= 2);\r\n\t\t\t\t\t\t\tconst sentence = arr[this.randBetween(0, arr.length)];\r\n\t\t\t\t\t\t\tconst cleanSentence = this.cleanPunctuation(sentence);\r\n\t\t\t\t\t\t\tconst name = this.randCharFromStr(cleanSentence, 2);\r\n\r\n\t\t\t\t\t\t\tvar str = {};\r\n\t\t\t\t\t\t\tstr.sentence = sentence.split(\"\");\r\n\t\t\t\t\t\t\tstr.name = name.split(\"\");\r\n\t\t\t\t\t\t\tstr.title = item.title;\r\n\t\t\t\t\t\t\tstr.author = item.author ? item.author : '佚名';\r\n\t\t\t\t\t\t\tstr.book = item.book;\r\n\t\t\t\t\t\t\tstr.dynasty = item.dynasty;\r\n\t\t\t\t\t\t\tResult.push(str);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.nameList = Result;\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.radio-group {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 10px;\r\n\t.radio {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\twidth: 50%;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n}\r\n\r\n.form-item {\r\n\tdisplay: flex;\r\n\tflex-wrap: nowrap;\r\n\theight: 10%;\r\n\tmargin: 10px, 0;\r\n\tbackground-color: #c8c7cc;\r\n\tborder-radius: 10px;\r\n\t.img {\r\n\t\twidth: 20px;\r\n\t\theight: 20px;\r\n\t\tpadding: 2px;\r\n\t}\r\n\t.button {\r\n\t\tbackground-color: #000000;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n}\r\n\r\n.name-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\t\t\n\t.name-container {\n\t\tmargin: 10px 20px;\n\t\tpadding: 5px 5px;\n\t\tbackground-color: #f1f1f1;\n\t\tborder: #333333 solid 1px;\n\t\t.name-info {\n\t\t\tfont-size: 26px;\n\t\t\tfont-weight:bold;\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: nowrap;\n\t\t}\n\t\t.sentence {\n\t\t\tpadding-top:5px;\n\t\t\tpadding-bottom: 5px;\n\t\t\tpadding-left: 30px;\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: nowrap;\n\t\t\t.active {\n\t\t\t\tcolor: #007AFF;\n\t\t\t}\n\t\t\t.black {\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.name-other {\n\t\t\t\n\t\t\tpading:2px 2px\n\t\t\t\n\t\t}\n\t}\n\t\n}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1619084291699\n      var cssReload = require(\"E:/HBuilderX.2.2.2.20190816.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=custom&index=0&blockType=script&module=tools&lang=wxs\"; export default mod; export * from \"-!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX.2.2.2.20190816.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=custom&index=0&blockType=script&module=tools&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}