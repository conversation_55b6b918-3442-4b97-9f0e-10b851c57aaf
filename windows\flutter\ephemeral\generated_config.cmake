# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\All install\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\androidproject\\liuyaozhanbuwannianli" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\All install\\flutter"
  "PROJECT_DIR=D:\\androidproject\\liuyaozhanbuwannianli"
  "FLUTTER_ROOT=D:\\All install\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\androidproject\\liuyaozhanbuwannianli\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\androidproject\\liuyaozhanbuwannianli"
  "FLUTTER_TARGET=D:\\androidproject\\liuyaozhanbuwannianli\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\androidproject\\liuyaozhanbuwannianli\\.dart_tool\\package_config.json"
)
