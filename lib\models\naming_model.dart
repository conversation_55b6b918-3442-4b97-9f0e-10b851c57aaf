import 'package:json_annotation/json_annotation.dart';

part 'naming_model.g.dart';

/// 诗词类型枚举
enum PoetryType {
  @JsonValue('唐诗')
  tangshi('唐诗'),
  @JsonValue('宋词')
  songci('宋词'),
  @JsonValue('诗经')
  shijing('诗经'),
  @JsonValue('楚辞')
  chuci('楚辞'),
  @JsonValue('乐府')
  yue<PERSON>('乐府'),
  @JsonValue('古诗')
  gushi('古诗');

  const PoetryType(this.displayName);
  final String displayName;
}

/// 诗词数据模型
@JsonSerializable()
class Poetry {
  final String content;
  final String title;
  final String? author;
  final String book;
  final String dynasty;

  const Poetry({
    required this.content,
    required this.title,
    this.author,
    required this.book,
    required this.dynasty,
  });

  factory Poetry.fromJson(Map<String, dynamic> json) => _$PoetryFromJson(json);
  Map<String, dynamic> toJson() => _$PoetryTo<PERSON>son(this);
}

/// 起名结果模型
@JsonSerializable()
class NamingResult {
  final String surname; // 姓氏
  final List<String> name; // 名字字符列表
  final List<String> sentence; // 诗句字符列表
  final String title; // 诗词标题
  final String author; // 作者
  final String book; // 出处
  final String dynasty; // 朝代
  final PoetryType poetryType; // 诗词类型
  final DateTime createdAt; // 创建时间

  const NamingResult({
    required this.surname,
    required this.name,
    required this.sentence,
    required this.title,
    required this.author,
    required this.book,
    required this.dynasty,
    required this.poetryType,
    required this.createdAt,
  });

  /// 获取完整姓名
  String get fullName => surname + name.join('');

  /// 获取完整诗句
  String get fullSentence => sentence.join('');

  /// 检查字符是否在名字中
  bool isCharInName(String char) {
    return name.contains(char);
  }

  factory NamingResult.fromJson(Map<String, dynamic> json) => _$NamingResultFromJson(json);
  Map<String, dynamic> toJson() => _$NamingResultToJson(this);
}

/// 测名结果模型
@JsonSerializable()
class NameAnalysisResult {
  final String fullName; // 完整姓名
  final String aiAnalysis; // AI分析结果
  final DateTime createdAt; // 创建时间

  const NameAnalysisResult({
    required this.fullName,
    required this.aiAnalysis,
    required this.createdAt,
  });

  factory NameAnalysisResult.fromJson(Map<String, dynamic> json) => _$NameAnalysisResultFromJson(json);
  Map<String, dynamic> toJson() => _$NameAnalysisResultToJson(this);
}

/// 起名请求模型
@JsonSerializable()
class NamingRequest {
  final String surname; // 姓氏
  final PoetryType poetryType; // 诗词类型

  const NamingRequest({
    required this.surname,
    required this.poetryType,
  });

  factory NamingRequest.fromJson(Map<String, dynamic> json) => _$NamingRequestFromJson(json);
  Map<String, dynamic> toJson() => _$NamingRequestToJson(this);
}

/// 测名请求模型
@JsonSerializable()
class NameAnalysisRequest {
  final String fullName; // 完整姓名

  const NameAnalysisRequest({
    required this.fullName,
  });

  factory NameAnalysisRequest.fromJson(Map<String, dynamic> json) => _$NameAnalysisRequestFromJson(json);
  Map<String, dynamic> toJson() => _$NameAnalysisRequestToJson(this);
}
