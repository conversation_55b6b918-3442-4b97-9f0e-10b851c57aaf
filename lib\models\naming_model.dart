import 'package:json_annotation/json_annotation.dart';

part 'naming_model.g.dart';

/// 诗词类型枚举
enum PoetryType {
  @JsonValue('唐诗')
  tangshi('唐诗'),
  @JsonValue('宋词')
  songci('宋词'),
  @JsonValue('诗经')
  shijing('诗经'),
  @JsonValue('楚辞')
  chuci('楚辞'),
  @JsonValue('乐府')
  yue<PERSON>('乐府'),
  @JsonValue('古诗')
  gushi('古诗');

  const PoetryType(this.displayName);
  final String displayName;
}

/// 诗词数据模型
@JsonSerializable()
class Poetry {
  final String content;
  final String title;
  final String? author;
  final String book;
  final String dynasty;

  const Poetry({
    required this.content,
    required this.title,
    this.author,
    required this.book,
    required this.dynasty,
  });

  factory Poetry.fromJson(Map<String, dynamic> json) => _$PoetryFromJson(json);
  Map<String, dynamic> toJson() => _$PoetryToJson(this);
}

/// 起名结果模型
@JsonSerializable()
class NamingResult {
  final String surname; // 姓氏
  final String givenName; // 名字
  final String fullName; // 完整姓名
  final Poetry poetry; // 诗词信息
  final List<String> selectedChars; // 选中的字符
  final List<String>? sentence; // 诗句字符列表（用于高亮显示）
  final DateTime createdAt; // 创建时间

  const NamingResult({
    required this.surname,
    required this.givenName,
    required this.fullName,
    required this.poetry,
    required this.selectedChars,
    this.sentence,
    required this.createdAt,
  });

  /// 获取完整诗句
  String get fullSentence => sentence?.join('') ?? '';

  /// 检查字符是否在名字中
  bool isCharInName(String char) {
    return selectedChars.contains(char);
  }

  /// 获取作者（处理空值）
  String get author => poetry.author ?? '佚名';

  /// 获取标题
  String get title => poetry.title;

  /// 获取出处
  String get book => poetry.book;

  /// 获取朝代
  String get dynasty => poetry.dynasty;

  factory NamingResult.fromJson(Map<String, dynamic> json) => _$NamingResultFromJson(json);
  Map<String, dynamic> toJson() => _$NamingResultToJson(this);
}

/// 测名结果模型
@JsonSerializable()
class NameAnalysisResult {
  final String fullName; // 完整姓名
  final String aiAnalysis; // AI分析结果
  final DateTime createdAt; // 创建时间

  const NameAnalysisResult({
    required this.fullName,
    required this.aiAnalysis,
    required this.createdAt,
  });

  factory NameAnalysisResult.fromJson(Map<String, dynamic> json) => _$NameAnalysisResultFromJson(json);
  Map<String, dynamic> toJson() => _$NameAnalysisResultToJson(this);
}

/// 起名请求模型
@JsonSerializable()
class NamingRequest {
  final String surname; // 姓氏
  final PoetryType poetryType; // 诗词类型

  const NamingRequest({
    required this.surname,
    required this.poetryType,
  });

  factory NamingRequest.fromJson(Map<String, dynamic> json) => _$NamingRequestFromJson(json);
  Map<String, dynamic> toJson() => _$NamingRequestToJson(this);
}

/// 测名请求模型
@JsonSerializable()
class NameAnalysisRequest {
  final String fullName; // 完整姓名

  const NameAnalysisRequest({
    required this.fullName,
  });

  factory NameAnalysisRequest.fromJson(Map<String, dynamic> json) => _$NameAnalysisRequestFromJson(json);
  Map<String, dynamic> toJson() => _$NameAnalysisRequestToJson(this);
}
