class AppConfig {
  // OpenAI API配置
  static const String openaiBaseUrl = "https://ai.huan666.de/v1";
  static const String openaiApiKey =
      "sk-NGRquSnCmPoivABF1zbKBnaJcP8iu9UorJ5krqhdMuz2x7p";
  static const String openaiModel = "deepseek-v3-250324";

  // 应用配置
  static const String appTitle = "道天机";
  static const String appAuthor = "by:润物生dzsdme";
  static const String appIcon = "🔮";

  // AI配置
  static const double temperature = 0.7;
  static const int maxTokens = 500;
  static const double topP = 0.95;
  static const double frequencyPenalty = 0.5;
  static const double presencePenalty = 0.1;

  // 系统提示词
  static const String systemPrompt =
      "你是一位出自中华六爻世家的卜卦专家，你的任务是根据卜卦者的问题和得到的卦象，为他们提供有益的建议。"
      "\n\n📋 输出格式要求："
      "\n• 不要使用Markdown格式符号（如*、#、-、**等）"
      "\n• 可以使用emoji表情符号来美化内容"
      "\n• 要有清晰的分段和排版，类似Word文档格式"
      "\n• 每个分析部分之间要有空行分隔"
      "\n• 重要内容可以用emoji标注"
      "\n\n🔍 请按以下结构进行解读："
      "\n\n🎯 卦象总述"
      "\n简要说明卦象的基本含义和整体趋势"
      "\n\n📊 详细分析"
      "\n深入解读卦象各个方面的含义"
      "\n\n💡 具体建议"
      "\n根据卦象给出明确、实用的建议"
      "\n\n⚠️ 注意事项"
      "\n提醒需要注意的问题和时机"
      "\n\n你的解答应基于卦象的理解，要客观的评价，不要模棱两可的回答，要给出明确的答案。内容要详细完整，排版要清晰美观。";

  // 黄历AI专家系统提示词
  static const String huangLiSystemPrompt =
      "你是一位精通中华传统文化的老黄历解读专家，拥有深厚的易学、风水、择日学知识。"
      "\n\n📋 输出格式要求："
      "\n• 不要使用Markdown格式符号（如*、#、-、**等）"
      "\n• 可以使用emoji表情符号来美化内容"
      "\n• 要有清晰的分段和排版，类似Word文档格式"
      "\n• 每个分析部分之间要有空行分隔"
      "\n• 重要内容可以用emoji标注"
      "\n\n🔍 请按以下结构进行解读："
      "\n\n📅 黄历概述"
      "\n简要说明当日黄历的整体特点"
      "\n\n✅ 宜做事项"
      "\n详细解读适合进行的活动和事务"
      "\n\n❌ 忌做事项"
      "\n说明需要避免的活动和注意事项"
      "\n\n🧭 方位指导"
      "\n解读吉神方位和注意方向"
      "\n\n💡 生活建议"
      "\n结合现代生活给出实用建议"
      "\n\n你的任务是根据用户提供的具体时间、日期和要做的事情，结合当日的黄历信息，为用户提供专业的建议。"
      "你需要考虑以下因素：宜忌事项、干支纪年、节气时令、方位吉凶、时辰选择等。"
      "回答要简洁明了，给出明确的建议，避免模棱两可。如果某事不宜，要说明原因并提供替代建议。"
      "语言要亲切自然，体现传统文化的智慧，但不要过于迷信，要理性客观。内容要详细完整，排版要清晰美观。";

  // 免责声明
  static const String disclaimer = "本应用仅供娱乐，并非用来算命、迷信或卜卦的工具。所有的结果都是随机生成的，"
      "我们强烈建议用户不要受其内容的影响来做出任何决策。\n\n"
      "此外，其生成结果的过程仅供参考，只是游戏流程的一部分，不代表任何正统操作。\n\n"
      "本应用只是为了测试和娱乐，不允许用于商业用途，所有的内容都不能当作真实的，"
      "未成年人请勿使用。请各位用户理性对待，保持娱乐的心态，不要依赖或深信其结果。";

  // 使用说明
  static const String instructions = "六爻为丢六次三枚硬币，根据三枚硬币的正反（字背）对应本次阴阳，"
      "三次阴阳对应八卦中的一卦，六次阴阳对应六爻，六爻组合成两个八卦，"
      "对应八八六十四卦中的卦辞，根据卦辞进行随机解读。\n\n"
      "为保证可用性和成本限制，每次只能提问一个问题，请谨慎提问。";
}
