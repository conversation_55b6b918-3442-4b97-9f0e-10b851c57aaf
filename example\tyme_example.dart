import 'package:daotianji/utils/tyme_helper.dart';

/// Tyme 工具类使用示例
void main() {
  print('=== Tyme (Lunar) 日历工具库使用示例 ===\n');

  // 1. 获取当前日期的农历信息
  print('1. 当前日期农历信息：');
  final currentLunar = TymeHelper.getCurrentLunar();
  print('农历：${TymeHelper.formatLunarDate(currentLunar)}');
  print('干支：${currentLunar.getDayInGanZhi()}');
  print('生肖：${currentLunar.getYearShengXiao()}');
  print('');

  // 2. 获取指定日期的详细黄历信息
  print('2. 指定日期详细黄历信息（2024年1月1日）：');
  final testDate = DateTime(2024, 1, 1);
  final detailedHuangLi = TymeHelper.getDetailedHuangLi(testDate);
  
  print('公历：${detailedHuangLi['solar']['date']}');
  print('农历：${detailedHuangLi['lunar']['date']}');
  print('星期：${detailedHuangLi['solar']['weekdayInChinese']}');
  print('生肖：${detailedHuangLi['zodiac']['shengXiao']}');
  print('星座：${detailedHuangLi['zodiac']['constellation']}');
  print('宜：${(detailedHuangLi['yiJi']['yi'] as List).take(3).join('、')}');
  print('忌：${(detailedHuangLi['yiJi']['ji'] as List).take(3).join('、')}');
  print('冲：${detailedHuangLi['shenSha']['chong']}');
  print('煞：${detailedHuangLi['shenSha']['sha']}');
  print('黄道吉日：${detailedHuangLi['huangDaoJiRi']['isHuangDao'] ? '是' : '否'}');
  print('');

  // 3. 获取八字信息
  print('3. 生辰八字信息（1986年5月29日12时）：');
  final birthDate = DateTime(1986, 5, 29, 12, 0);
  final baziDetails = TymeHelper.getBaZiDetails(birthDate);
  
  print('出生日期：');
  print('  公历：${baziDetails['birthDate']['solar']}');
  print('  农历：${baziDetails['birthDate']['lunar']}');
  print('  星期：${baziDetails['birthDate']['weekday']}');
  
  print('八字：');
  print('  年柱：${baziDetails['baZi']['year']}');
  print('  月柱：${baziDetails['baZi']['month']}');
  print('  日柱：${baziDetails['baZi']['day']}');
  print('  时柱：${baziDetails['baZi']['time']}');
  
  print('纳音：');
  print('  年：${baziDetails['naYin']['year']}');
  print('  月：${baziDetails['naYin']['month']}');
  print('  日：${baziDetails['naYin']['day']}');
  print('  时：${baziDetails['naYin']['time']}');
  
  print('五行：');
  print('  年：${baziDetails['wuXing']['year']}');
  print('  月：${baziDetails['wuXing']['month']}');
  print('  日：${baziDetails['wuXing']['day']}');
  print('  时：${baziDetails['wuXing']['time']}');
  
  print('生肖：${baziDetails['shengXiao']}');
  print('星座：${baziDetails['constellation']}');
  print('');

  // 4. 获取简化黄历信息（适用于日历显示）
  print('4. 简化黄历信息（适用于日历显示）：');
  final simpleHuangLi = TymeHelper.getSimpleHuangLi(testDate);
  print('公历：${simpleHuangLi['solarDate']}');
  print('农历：${simpleHuangLi['lunarDate']}');
  print('干支：${simpleHuangLi['ganZhi']}');
  print('星宿：${simpleHuangLi['xingXiu']}');
  print('天神：${simpleHuangLi['tianShen']}');
  print('黄道类型：${simpleHuangLi['huangDaoType']}');
  print('');

  // 5. 获取月份所有日期信息
  print('5. 2024年1月前5天的日期信息：');
  final monthDates = TymeHelper.getMonthDates(2024, 1);
  for (int i = 0; i < 5 && i < monthDates.length; i++) {
    final dayInfo = monthDates[i];
    print('${dayInfo['solarDate']} ${dayInfo['lunarDate']} ${dayInfo['weekday']} ${dayInfo['ganZhi']}');
  }
  print('');

  // 6. 节气信息
  print('6. 节气信息：');
  final currentJieQi = TymeHelper.getCurrentJieQi();
  final nextJieQi = TymeHelper.getNextJieQi();
  print('当前节气：${currentJieQi.isNotEmpty ? currentJieQi : '无'}');
  print('下个节气：${nextJieQi.isNotEmpty ? nextJieQi : '无'}');
  print('');

  // 7. 黄道吉日判断
  print('7. 黄道吉日判断：');
  final dates = [
    DateTime(2024, 1, 1),
    DateTime(2024, 1, 2),
    DateTime(2024, 1, 3),
  ];
  
  for (final date in dates) {
    final isHuangDao = TymeHelper.isHuangDaoJiRi(date);
    final solar = TymeHelper.getSolarFromDate(date);
    print('${solar.toYmd()} ${isHuangDao ? '黄道吉日' : '黑道凶日'}');
  }
  
  print('\n=== 示例结束 ===');
}
