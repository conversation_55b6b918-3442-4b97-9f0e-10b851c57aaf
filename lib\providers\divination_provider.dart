import 'package:flutter/foundation.dart';
import '../models/gua_model.dart';
import '../models/api_config.dart';
import '../services/divination_service.dart';
import '../services/openai_service.dart';
import '../services/universal_history_service.dart';
import '../utils/markdown_cleaner.dart';

class DivinationProvider with ChangeNotifier {
  final DivinationService _divinationService = DivinationService();
  final OpenAIService _openAIService = OpenAIService();

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  bool _isInputDisabled = false;
  DivinationResult? _currentResult;
  bool _useStreamOutput = true; // 默认使用流式输出
  bool _formatAsPlainText = true; // 默认转换为纯文本

  List<ChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  bool get isInputDisabled => _isInputDisabled;
  DivinationResult? get currentResult => _currentResult;
  bool get useStreamOutput => _useStreamOutput;
  bool get formatAsPlainText => _formatAsPlainText;

  set useStreamOutput(bool value) {
    _useStreamOutput = value;
    notifyListeners();
  }

  set formatAsPlainText(bool value) {
    _formatAsPlainText = value;
    notifyListeners();
  }

  DivinationProvider() {
    _initializeMessages();
  }

  void _initializeMessages() {
    _messages = [
      ChatMessage(
        role: 'assistant',
        content: '心中默念所求之事!!!',
      ),
    ];
  }

  void _addMessage(String role, String content) {
    _messages.add(ChatMessage(role: role, content: content));
    notifyListeners();
  }

  void _addCoinFlipMessage(CoinResult coinResult) {
    _messages.add(ChatMessage(
      role: 'assistant',
      content: coinResult.description,
      type: MessageType.coinFlip,
      coinResult: coinResult,
    ));
    notifyListeners();
  }

  // 更新最后一条消息的内容
  void _updateLastMessage(String content) {
    if (_messages.isNotEmpty && _messages.last.role == 'assistant') {
      final lastMsg = _messages.last;
      _messages.removeLast();
      _messages.add(ChatMessage(
        role: lastMsg.role,
        content: content,
        timestamp: lastMsg.timestamp,
        type: lastMsg.type,
        coinResult: lastMsg.coinResult,
      ));
      notifyListeners();
    }
  }

  Future<void> performDivination(String question,
      {ApiConfig? apiConfig}) async {
    if (_isInputDisabled) return;

    _isInputDisabled = true;
    _isLoading = true;
    _addMessage('user', question);
    notifyListeners();

    try {
      // 执行占卜
      final result = await _divinationService.performDivination(question);
      _currentResult = result;

      // 显示投币过程 - 使用铜钱抛掷动画
      for (final coinResult in result.coinResults) {
        await Future.delayed(const Duration(milliseconds: 800));
        _addCoinFlipMessage(coinResult);
      }

      // 显示首卦
      await Future.delayed(const Duration(milliseconds: 500));
      _addMessage('assistant', '您的首卦为：${result.firstGua}');

      // 显示次卦
      await Future.delayed(const Duration(milliseconds: 500));
      _addMessage('assistant', '您的次卦为：${result.secondGua}');

      // 显示卦象信息
      await Future.delayed(const Duration(milliseconds: 500));
      _addMessage('assistant', '''
六爻结果: ${result.finalGua}  
卦名为：${result.guaInfo.name}   
${result.guaInfo.des}   
卦辞为：${result.guaInfo.sentence}   
''');

      // 获取AI解读
      if (_useStreamOutput) {
        // 使用流式输出
        _addMessage('assistant', ''); // 添加空消息作为占位符

        final stream = _openAIService.streamInterpretDivination(
          question: question,
          gua: result.finalGua,
          guaName: result.guaInfo.name,
          guaDes: result.guaInfo.des,
          guaSentence: result.guaInfo.sentence,
          config: apiConfig,
          formatAsPlainText: _formatAsPlainText,
        );

        String accumulatedText = '';
        await for (final chunk in stream) {
          // 使用温和清理，保留emoji符号
          final cleanChunk = MarkdownCleaner.gentleClean(chunk);
          accumulatedText += cleanChunk;
          _updateLastMessage(accumulatedText);
        }

        // 保存完整的卜卦记录
        await _saveRecord(question, result.copyWith(aiInterpretation: accumulatedText));

        _isLoading = false;
        notifyListeners();
      } else {
        // 使用传统输出
        final interpretation = await _openAIService.interpretDivination(
          question: question,
          gua: result.finalGua,
          guaName: result.guaInfo.name,
          guaDes: result.guaInfo.des,
          guaSentence: result.guaInfo.sentence,
          config: apiConfig,
          formatAsPlainText: _formatAsPlainText,
        );

        _isLoading = false;
        _addMessage('assistant', interpretation);

        // 保存完整的卜卦记录
        await _saveRecord(question, result.copyWith(aiInterpretation: interpretation));
      }

      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      _isLoading = false;
      _addMessage('assistant', '抱歉，占卜过程中出现了错误：$e');

      // 即使出错也尝试保存基本的卜卦记录（不包含AI解读）
      if (_currentResult != null) {
        await _saveRecord(question, _currentResult!);
      }
    } finally {
      // 重新启用输入
      _isInputDisabled = false;
      _isLoading = false;
      notifyListeners();
    }
  }

  // 保存卜卦记录
  Future<void> _saveRecord(String question, DivinationResult result) async {
    try {
      await UniversalHistoryService.addDivinationRecord(question, result);
      debugPrint('Divination record saved successfully');
    } catch (e) {
      debugPrint('Error saving divination record: $e');
      // 不影响主流程，只记录错误
    }
  }

  void reset() {
    _messages.clear();
    _isLoading = false;
    _isInputDisabled = false;
    _currentResult = null;
    _initializeMessages();
    notifyListeners();
  }


}
