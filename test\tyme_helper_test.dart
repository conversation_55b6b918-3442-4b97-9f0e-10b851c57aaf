import 'package:flutter_test/flutter_test.dart';
import 'package:daotianji/utils/tyme_helper.dart';

void main() {
  group('TymeHelper Tests', () {
    test('应该正确获取当前农历信息', () {
      final lunar = TymeHelper.getCurrentLunar();
      
      expect(lunar, isNotNull);
      expect(lunar.getYear(), greaterThan(0));
      expect(lunar.getMonth(), greaterThan(0));
      expect(lunar.getDay(), greaterThan(0));
    });

    test('应该正确获取指定日期的农历信息', () {
      final testDate = DateTime(2024, 1, 1);
      final lunar = TymeHelper.getLunarFromDate(testDate);
      
      expect(lunar, isNotNull);
      expect(lunar.getYear(), equals(2023)); // 2024年1月1日对应农历2023年
      expect(lunar.getMonth(), greaterThan(0));
      expect(lunar.getDay(), greaterThan(0));
    });

    test('应该正确获取八字信息', () {
      final testDate = DateTime(1986, 5, 29, 12, 0); // 测试日期
      final eightChar = TymeHelper.getEightChar(testDate);

      expect(eightChar, isNotNull);
      expect(eightChar.getYear(), isNotEmpty);
      expect(eightChar.getMonth(), isNotEmpty);
      expect(eightChar.getDay(), isNotEmpty);
      expect(eightChar.getTime(), isNotEmpty);
    });

    test('应该正确获取详细黄历信息', () {
      final testDate = DateTime(2024, 1, 1);
      final huangLi = TymeHelper.getDetailedHuangLi(testDate);
      
      expect(huangLi, isNotNull);
      expect(huangLi['solar'], isNotNull);
      expect(huangLi['lunar'], isNotNull);
      expect(huangLi['eightChar'], isNotNull);
      expect(huangLi['zodiac'], isNotNull);
      expect(huangLi['yiJi'], isNotNull);
      expect(huangLi['shenSha'], isNotNull);
      expect(huangLi['jiShenFangWei'], isNotNull);
      
      // 验证具体字段
      expect(huangLi['solar']['date'], isNotEmpty);
      expect(huangLi['lunar']['date'], isNotEmpty);
      expect(huangLi['zodiac']['shengXiao'], isNotEmpty);
      expect(huangLi['zodiac']['constellation'], isNotEmpty);
    });

    test('应该正确获取简化黄历信息', () {
      final testDate = DateTime(2024, 1, 1);
      final simpleHuangLi = TymeHelper.getSimpleHuangLi(testDate);
      
      expect(simpleHuangLi, isNotNull);
      expect(simpleHuangLi['solarDate'], isNotEmpty);
      expect(simpleHuangLi['lunarDate'], isNotEmpty);
      expect(simpleHuangLi['weekday'], isNotEmpty);
      expect(simpleHuangLi['ganZhi'], isNotEmpty);
      expect(simpleHuangLi['shengXiao'], isNotEmpty);
      expect(simpleHuangLi['constellation'], isNotEmpty);
    });

    test('应该正确判断黄道吉日', () {
      final testDate = DateTime(2024, 1, 1);
      final isHuangDao = TymeHelper.isHuangDaoJiRi(testDate);
      
      expect(isHuangDao, isA<bool>());
    });

    test('应该正确获取节气信息', () {
      final currentJieQi = TymeHelper.getCurrentJieQi();
      final nextJieQi = TymeHelper.getNextJieQi();
      
      // 节气可能为空（如果当前不在节气日）
      expect(currentJieQi, isA<String>());
      expect(nextJieQi, isA<String>());
    });

    test('应该正确格式化日期显示', () {
      final testDate = DateTime(2024, 1, 1);
      final lunar = TymeHelper.getLunarFromDate(testDate);
      final solar = TymeHelper.getSolarFromDate(testDate);

      final lunarFormatted = TymeHelper.formatLunarDate(lunar);
      final solarFormatted = TymeHelper.formatSolarDate(solar);

      expect(lunarFormatted, contains('年'));
      // 农历月份可能显示为"冬"等季节名称，所以不强制要求包含"月"
      expect(solarFormatted, contains('2024年'));
      expect(solarFormatted, contains('1月'));
      expect(solarFormatted, contains('1日'));
    });

    test('应该正确获取生辰八字详细信息', () {
      final testDate = DateTime(1986, 5, 29, 12, 0);
      final baziDetails = TymeHelper.getBaZiDetails(testDate);
      
      expect(baziDetails, isNotNull);
      expect(baziDetails['birthDate'], isNotNull);
      expect(baziDetails['baZi'], isNotNull);
      expect(baziDetails['naYin'], isNotNull);
      expect(baziDetails['shengXiao'], isNotEmpty);
      expect(baziDetails['constellation'], isNotEmpty);
      expect(baziDetails['wuXing'], isNotNull);
      
      // 验证八字四柱
      final bazi = baziDetails['baZi'];
      expect(bazi['year'], isNotEmpty);
      expect(bazi['month'], isNotEmpty);
      expect(bazi['day'], isNotEmpty);
      expect(bazi['time'], isNotEmpty);
      
      // 验证纳音
      final nayin = baziDetails['naYin'];
      expect(nayin['year'], isNotEmpty);
      expect(nayin['month'], isNotEmpty);
      expect(nayin['day'], isNotEmpty);
      expect(nayin['time'], isNotEmpty);
      
      // 验证五行
      final wuxing = baziDetails['wuXing'];
      expect(wuxing['year'], isNotEmpty);
      expect(wuxing['month'], isNotEmpty);
      expect(wuxing['day'], isNotEmpty);
      expect(wuxing['time'], isNotEmpty);
    });

    test('应该正确获取月份所有日期信息', () {
      final monthDates = TymeHelper.getMonthDates(2024, 1);
      
      expect(monthDates, isNotEmpty);
      expect(monthDates.length, equals(31)); // 2024年1月有31天
      
      // 验证第一天的信息
      final firstDay = monthDates.first;
      expect(firstDay['solarDate'], contains('2024-01-01'));
      expect(firstDay['lunarDate'], isNotEmpty);
      expect(firstDay['weekday'], isNotEmpty);
    });

    test('应该正确处理特殊日期', () {
      // 测试闰年2月29日
      final leapDate = DateTime(2024, 2, 29);
      final lunar = TymeHelper.getLunarFromDate(leapDate);
      
      expect(lunar, isNotNull);
      expect(lunar.getYear(), greaterThan(0));
      
      // 测试年末日期
      final yearEndDate = DateTime(2023, 12, 31);
      final yearEndLunar = TymeHelper.getLunarFromDate(yearEndDate);
      
      expect(yearEndLunar, isNotNull);
      expect(yearEndLunar.getYear(), greaterThan(0));
    });

    test('应该正确处理不同时辰的八字', () {
      final baseDate = DateTime(1986, 5, 29);
      
      // 测试不同时辰
      final times = [0, 6, 12, 18, 23]; // 子时、卯时、午时、酉时、亥时
      
      for (final hour in times) {
        final testDate = DateTime(baseDate.year, baseDate.month, baseDate.day, hour);
        final eightChar = TymeHelper.getEightChar(testDate);

        expect(eightChar.getTime(), isNotEmpty);
        expect(eightChar.getTimeNaYin(), isNotEmpty);
        expect(eightChar.getTimeWuXing(), isNotEmpty);
      }
    });
  });
}
